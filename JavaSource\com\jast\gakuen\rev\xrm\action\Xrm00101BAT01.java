package com.jast.gakuen.rev.xrm.action;

import java.io.File;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jp.co.fit.vfreport.Vrw32;

import com.jast.gakuen.framework.GakuenException;
import com.jast.gakuen.framework.batch.BatchBase;
import com.jast.gakuen.framework.batch.BatchConst;
import com.jast.gakuen.framework.batch.BatchUtil;
import com.jast.gakuen.framework.constant.SyConst;
import com.jast.gakuen.framework.db.DbException;
import com.jast.gakuen.framework.util.UtilDate;
import com.jast.gakuen.framework.util.UtilIni;
import com.jast.gakuen.framework.util.UtilLog;
import com.jast.gakuen.framework.util.UtilPdfWriter;
import com.jast.gakuen.framework.util.UtilStr;
import com.jast.gakuen.rev.co.db.dao.VCobGakZaiDAO;
import com.jast.gakuen.rev.co.util.UtilBarcode;
import com.jast.gakuen.rev.co.util.UtilCo;
import com.jast.gakuen.rev.gh.action.bean.GhPayhBean;
import com.jast.gakuen.rev.gh.constant.GhConstant;
import com.jast.gakuen.rev.gh.db.dao.GhcPayDAO;
import com.jast.gakuen.rev.gh.db.dao.GheGakAtskDAO;
import com.jast.gakuen.rev.gh.db.dao.GheGakseiDAO;
import com.jast.gakuen.rev.gh.db.dao.GheHsyDAO;
import com.jast.gakuen.rev.gh.db.dao.GheSgksDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgFurikomiDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPayhDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPayhItemDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPaywBunDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPaywItemDAO;
import com.jast.gakuen.rev.gh.db.dao.GhiEnnoDAO;
import com.jast.gakuen.rev.gh.db.dao.GhpSotDAO;
import com.jast.gakuen.rev.gh.db.dao.GhpSotGakDAO;
import com.jast.gakuen.rev.gh.db.dao.GhpSotSgksDAO;
import com.jast.gakuen.rev.gh.db.dao.JoinBunkatsuGakuDAO;
import com.jast.gakuen.rev.gh.db.dao.JoinPaywBunInfoDAO;
import com.jast.gakuen.rev.gh.db.dao.JoinStudentPayInfoDAO;
import com.jast.gakuen.rev.gh.db.dao.JoinUtiwakeGakuDAO;
import com.jast.gakuen.rev.gh.db.entity.GhcPayAR;
import com.jast.gakuen.rev.gh.db.entity.GheGakAtskAR;
import com.jast.gakuen.rev.gh.db.entity.GheGakseiAR;
import com.jast.gakuen.rev.gh.db.entity.GheHsyAR;
import com.jast.gakuen.rev.gh.db.entity.GhgFurikomiAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhItemAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPaywBunAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPaywItemAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPaywItemARComparator;
import com.jast.gakuen.rev.gh.db.entity.GhiEnnoAR;
import com.jast.gakuen.rev.gh.db.entity.GhpSotAR;
import com.jast.gakuen.rev.gh.db.entity.GhpSotGakAR;
import com.jast.gakuen.rev.gh.db.entity.GhpSotSgksAR;
import com.jast.gakuen.rev.gh.db.entity.JoinBunkatsuGakuAR;
import com.jast.gakuen.rev.gh.db.entity.JoinPaywBunInfoAR;
import com.jast.gakuen.rev.gh.db.entity.JoinStudentPayInfoAR;
import com.jast.gakuen.rev.gh.db.entity.JoinUtiwakeGakuAR;
import com.jast.gakuen.rev.gh.util.UtilGhGyomuNendo;
import com.jast.gakuen.rev.gh.util.UtilGhStudentName;
import com.jast.gakuen.rev.xrm.Xrm00101T01;
import com.jast.gakuen.rev.xrm.action.bean.Xrm00101L01Bean;
import com.jast.gakuen.rev.xrm.action.bean.Xrm001OutDataBean;
import com.jast.gakuen.rev.xrm.business.BizXrmComInfo;
import com.jast.gakuen.rev.xrm.constant.XrmIniConst;
import com.jast.gakuen.rev.xrm.db.dao.XrmJoinPaywBunInfoDAO;
import com.jast.gakuen.rev.xrm.db.dao.XrmPayDAO;
import com.jast.gakuen.rev.xrm.db.dao.XrmPayeasyRrkDAO;
import com.jast.gakuen.rev.xrm.db.dao.XrmPayeasyRrkItemDAO;
import com.jast.gakuen.rev.xrm.db.dao.XrmPayhDAO;
import com.jast.gakuen.rev.xrm.db.dao.XrmPaywBunDAO;
import com.jast.gakuen.rev.xrm.db.entity.XrmPayAR;
import com.jast.gakuen.rev.xrm.db.entity.XrmPayeasyRrkAR;
import com.jast.gakuen.rev.xrm.db.entity.XrmPayeasyRrkItemAR;
import com.jast.gakuen.rev.xrm.db.entity.XrmPayhAR;
import com.jast.gakuen.rev.xrm.db.entity.XrmPaywBunAR;
import com.jast.gakuen.rev.xrm.util.XrmUtilConvert;
import com.jast.gakuen.rev.xrm.util.XrmUtilCsvWriter;
import com.jast.gakuen.rev.xrm.util.XrmUtilIni;
import com.jast.gakuen.rev.xrx.db.dao.XrxMeiKanriKmkDAO;
import com.jast.gakuen.rev.xrx.db.entity.XrxMeiKanriKmkAR;
import com.jast.gakuen.system.co.constant.code.AtesakiKbn;
import com.jast.gakuen.system.co.constant.code.GakusotsuKbn;
import com.jast.gakuen.system.co.constant.code.KeishoKbn;
import com.jast.gakuen.system.co.constant.code.ProductKbn;
import com.jast.gakuen.system.co.db.dao.CobGakAtskDAO;
import com.jast.gakuen.system.co.db.dao.CobGakHsyDAO;
import com.jast.gakuen.system.co.db.dao.CobGakseiDAO;
import com.jast.gakuen.system.co.db.dao.CobGaksekiDAO;
import com.jast.gakuen.system.co.db.dao.CobSgksDAO;
import com.jast.gakuen.system.co.db.dao.CobSotDAO;
import com.jast.gakuen.system.co.db.dao.CobSotGakDAO;
import com.jast.gakuen.system.co.db.dao.CobSotSgksDAO;
import com.jast.gakuen.system.co.db.dao.CozAtnaKsyoDAO;
import com.jast.gakuen.system.co.db.dao.CozGakoDAO;
import com.jast.gakuen.system.co.db.entity.CobGakAtskAR;
import com.jast.gakuen.system.co.db.entity.CobGakHsyAR;
import com.jast.gakuen.system.co.db.entity.CobGakseiAR;
import com.jast.gakuen.system.co.db.entity.CobGaksekiAR;
import com.jast.gakuen.system.co.db.entity.CobSgksAR;
import com.jast.gakuen.system.co.db.entity.CobSotAR;
import com.jast.gakuen.system.co.db.entity.CobSotGakAR;
import com.jast.gakuen.system.co.db.entity.CobSotSgksAR;
import com.jast.gakuen.system.co.db.entity.CozAtnaKsyoAR;

/**
 * 請求書出力用バッチクラス
 */
public class Xrm00101BAT01 extends BatchBase {
	
	/**画面区分*/
	private 			String kbn	= null;
	/**PDFタイトル*/
	private 			String title	=null;
	/**発行日付*/
	private 			Date hakkoDate	= null;
	/**通信区分*/
	boolean 			tusinKbn=false;
	/**通信欄*/
	private 			String[] tusinnran	=null;
	/**出力区分*/
	private 			String syutkbn	=null;
	/**学年*/
	private 			String strGakunen	=null;
	/**就学種別*/
	private			String syugaksyubetu=null;
	/**所属学科コード*/
	private 			String szkGakka		=null;
	/**異動出学種別*/
	private 			String idoSbt	=null;
	/**異動開始日*/
	private 			Date idoDateFrom	=null;
	/**異動終了日*/
	private 			Date idoDateTo		=null;
	/**チェック納付金リスト*/
	private 			List outPayList =null;
	/**管理番号リスト*/
	private 			Map kanriList =null;
	/**学費年度*/
	private 			int gnend ;
	/** 出力データ件数 */
	private int outdataCount;
	/** 学籍番号のリスト */
	private ArrayList outGakusekiCdList = new ArrayList();
	/** 納付金情報 */
	private List utiwakeList = null;
	
	/** 処理対象件数 */
	private int updateCnt;
	
	/** 敬称区分名称（学生） */
	private String keisyoKbnNameGak = "";
	
	/** 敬称区分名称（保証人） */
	private String keisyoKbnNameHsy = "";
	
	/** 退避用管理番号(詳細データ作成用)*/
	private long kanriNoOld = 0L;
	
	/**業務コード*/
	private String gyomcd = ""; 
	/**各カウント*/
	private int total = 0;
	private int normalCount=0;
	private int errerCount=0;
	
	/**納入期限*/
	private Date nonyuDate=null;
	
	/**有効期限*/
	private Date yukoDate=null;
	
	/** バッチ区分 */
	private String batKbn = null;
	
	/** バッチ実行確認有無 */
	private boolean execConfirm = true;
	
	/** 出力データ（基本） */
	private List baseOutdataList = null;
	
	/** 出力データ（CSV） */
	private List outdataCsvList = null;
	
	/** 出力データ（PDF） */
	private List outdataPdfList = null;
	
	/** 文字コード: SJIS */
	private String charset = SyConst.CHARSET_SJIS;
	
	private List outdt = new ArrayList();
	
	//出力条件
	/** ページコード */
	private Xrm00101T01 pc = null;
	
	/** ワーニングカウント */
	private int warcnt = 0;
	
	/** エラーカウント数 */
	private int errcnt  = 0;
	
	/** 未処理件数 */
	private int undocnt  = 0;
	
	/** 未処理件数 */
	private int allCnt  = 0;
	/** ファイルID(PDF) */
	public static final String PDF_FILEID_CHKLIST			= "Xrm001PDF01";
	/** ファイルID(CSV) */
	public static final String CSV_FILEID					= "Xrm001CSV01";
	
	/** 空文字 */
	private final static String BLANK = "";
	
	/** 納付金指定出力 */
	private final String PAY = "PAY";
	
	/** 納付金・学生指定出力 */
	private final String PAY_GAK = "PAY_GAK";
	
	/** 学生指定出力 */
	private final String GAK = "GAK";
	
	/**出力区分(再発行)*/
	private final String RE_HAKKO ="3";
	
	/**出力区分(未出力)*/
	private final String NEW_HAKKO ="2";
	
	/**出力区分(発行)*/
	private final String HAKKO ="1";
	
	/** 画像フォルダ */
	private File imgdir;
	
	/**担当部署種別コード*/
	private final String tantosyubet="M04";
	
	/**収納機関、委託者検索用種別コード*/
	private final String syunokikanCd ="M03";
	
	/**MUF企業科目コード*/
	private final String kigyoCd ="03";
	
	/**収納機関科目コード*/
	private final String syunokmk ="01";
	
	/**委託者科目コード*/
	private final String itakkmk ="02"; 
	
	/**ファイルパス*/
	String PDF_FILEID = "Xrm001PDF01";
	
	/**SVSの備考を設定*/
	private final String SVS1 = "この払込票は、払込金額が３０万円を超えていますので、";
	private final String SVS2 = "コンビニエンスストアではお取扱いできません。「Ｐａ";
	private final String SVS3 = "ｙ－ｅａｓｙ（ペイジー）」でお支払いください。";
	
	
	/**担当部署種別科目コード(教師教育リサーチセンター)*/
	private final String resercTanto = "03";
	/**担当部署種別科目コード(スクーリング)*/
	private final String schoolTanto = "02";
	/**担当部署種別科目コード(その他)*/
	private final String sonotaTanto = "01";
	/** フォーマット */
	static public final String DATE_PATTERN ="yyyymmdd";
	
	/**csv出力時に使用するデータ*/
	List csvoutlist =new ArrayList();
	
	/** バーコード設定基準金額 */
	private int intBaseKingakChk = 0;

	
	
	
	/**csvヘッダー情報*/
	private static final String[] CSV_HEADDER_TITLE ={
			"データ区分",
			"処理区分",
			"顧客番号",
			"確認番号",
			"コンビニネットコード",
			"請求金額",
			"元金",
			"延滞金額",
			"消費税",
			"請求内容カナ",
			"請求内容漢字",
			"氏名カナ",
			"氏名漢字",
			"",
			"",
			"",
			"",
			"",
			"",
			"",
			"請求情報有効期限"	
	};
	/**CSV出力項目(データ区分)*/
	public static final String dateKbn = "2";
	/**CSV出力項目(処理区分)*/
	public static final String syoriKbn = "1";
	/** 所属学科組織ソート用定数（在校生） */
	private final String ZAIKOSEI = "0";
	/** 所属学科組織ソート用定数（学費学生） */
	private final String GAKUHIGAK = "1";
	/** 所属学科組織ソート用定数（卒業生） */
	private final String SOTUGYOU = "2";
	/** 所属学科組織ソート用定数（ダミー並び順No） */
	private final String DUMMY_LINE_NO = "00";
	/**PDF画像パス*/
	private final String PAYEASY_IMAGE=UtilIni.getProductParameter("XRM","PATH","PAYEASYVIEW"); 
	private final String SYUNO_IMAGE=UtilIni.getProductParameter("XRM","PATH","SYUNOINVIEW"); 
	private final String SYUNO_IMAGE_B=UtilIni.getProductParameter("XRM","PATH","SYUNOINVIEW2"); 
	
	/** PDF出力クラス */
	private Vrw32 vrw = null;
	
	
	/**
	 * コンストラクタ
	 * @param batKbn バッチ区分
	 * <pre>
	 * BatchConst.BATKBN_P PDF出力
	 * BatchConst.BATKBN_C CSV出力
	 * </pre>
	 * @param execConfirm バッチ実行確認有無
	 * <pre>
	 * true カウント処理実行後にバッチ実行有無の確認を行う
	 * false カウント処理実行後にバッチ実行有無の確認を行わない
	 * </pre>
	 */
	public Xrm00101BAT01(String batKbn, boolean execConfirm) {
		if (!BatchConst.BATKBN_C.equals(batKbn)
				&& !BatchConst.BATKBN_P.equals(batKbn)) {
			throw new RuntimeException("無効なバッチ区分です。batKbn=" + batKbn);
		}
		this.batKbn = batKbn;
		this.execConfirm = execConfirm;
	}
	
	/**
	 * 出力条件を設定します。<br>
	 * バッチをスレッド実行する前にあらかじめ呼び出し元で設定します。(納付金指定)
	 
	 */
	public void setOutputConditions(
			String kbn,
			String title,
			Date hakkoDate,
			boolean tusinKbn,
			String[] tusinnran,
			int gnend,
			String syutkbn,
			String strGakunen,
			String syugaksyubetu,
			String szkGakka,
			String idoSbt,
			Date idoDateFrom,
			Date idoDateTo,
			Map kanriList,
			List payList,
			String gyomcd,
			Date nonyuDate,
			Date yukoDate
	){
		this.kbn = kbn;
		this.title = title;
		this.hakkoDate = hakkoDate;
		this.tusinKbn = tusinKbn;
		this.tusinnran= tusinnran;
		this.gnend=gnend;
		this.syutkbn = syutkbn;
		this.strGakunen = strGakunen;
		this.syugaksyubetu = syugaksyubetu;
		this.szkGakka = szkGakka;
		this.idoSbt = idoSbt;
		this.idoDateFrom = idoDateFrom;
		this.idoDateTo = idoDateTo;
		this.kanriList= kanriList;
		this.outPayList = payList;
		this.gyomcd=gyomcd;
		this.nonyuDate=nonyuDate;
		this.yukoDate=yukoDate;
		
	}
	
	/**
	 * 出力条件を設定します。<br>
	 * バッチをスレッド実行する前にあらかじめ呼び出し元で設定します。(学生納付金指定)
	 */
	public void setOutputConditions(
			String kbn,
			String title,
			Date hakkoDate,
			boolean tusinKbn,
			String tusinnran[],
			int gnend,
			String syutkbn,
			String strGakunen,
			Map kanriList,
			List payList,
			String gyomcd,
			Date nonyuDate,
			Date yukoDate
	){
		this.kbn = kbn;
		this.title = title;
		this.hakkoDate = hakkoDate;
		this.tusinKbn = tusinKbn;
		this.tusinnran= tusinnran;
		this.gnend=gnend;
		this.syutkbn = syutkbn;
		this.strGakunen = strGakunen;
		this.kanriList= kanriList;
		this.outPayList = payList;
		this.gyomcd=gyomcd;
		this.nonyuDate=nonyuDate;
		this.yukoDate=yukoDate;
		
	}
	
	/**
	 * 出力条件を設定します。<br>
	 * バッチをスレッド実行する前にあらかじめ呼び出し元で設定します。(学生納付金指定)
	 */
	public void setOutputConditions(
			String kbn,
			String title,
			Date hakkoDate,
			boolean tusinKbn,
			String tusinnran[],
			int gnend,
			String syutkbn,
			Map kanriList,
			List payList,
			Date nonyuDate,
			Date yukoDate
			
	){
		this.kbn = kbn;
		this.title = title;
		this.hakkoDate = hakkoDate;
		this.tusinKbn = tusinKbn;
		this.tusinnran= tusinnran;
		this.gnend=gnend;
		this.syutkbn = syutkbn;
		this.kanriList= kanriList;
		this.outPayList = payList;
		this.nonyuDate=nonyuDate;
		this.yukoDate=yukoDate;
	}
	
	
	
	/**
	 * バッチ処理を停止する.<BR>
	 *
	 * @return 処理結果
	 */
	protected String stop() {
		
		try {
			//トランザクションをロールバック
			rollback();
		} catch (Exception e) {
			
			return BatchConst.STAT_ABORT_ERROR;
			
		} finally {
			
			
		}
		
		// エラー状態によって戻り値を変更する.
		if (errcnt != 0) {
			return BatchConst.STAT_ABORT_ERROR;
		} else {
			return BatchConst.STAT_ABORT_SUCCESS;
		}
	}
	
	/**
	 * 最大カウント件数を計算する.<BR>
	 * ※カウント件数は、jugyoCdListのサイズと等しい.<BR>
	 *
	 * @return 処理結果
	 */
	protected String count() {
		
		try{
			//管理番号が取得できなかった場合
			if(kanriList.size()==0){
				total=0;
			}else{
				//納付金指定画面
				if(kbn.equals("PAY")){
					//納付金指定出力
					baseOutdataList = getBaseOutData(PAY);
				}else if(kbn.equals("PAY_GAK")){
					//納付金、学生指定
					baseOutdataList = getBaseOutData(PAY_GAK);
				}else{
					//学生指定
					baseOutdataList=getBaseOutData(GAK);
				}
				//処理件数を取得
				total = baseOutdataList.size();
			}
			//処理対象件数を更新する
			this.updateMaxCount(total);
			
			
		}catch(Exception e){
			UtilLog.error(this.getClass(), e);
			return BatchConst.STAT_END_ERROR;
		}		
		return BatchConst.STAT_END_COUNT;
	}
	
	
	/**
	 * PDF・CSV出力データの基本情報を取得します。
	 * @return　出力データのList
	 * @exception Exception
	 */
	private List getBaseOutData(String kbn) throws Exception {
		int sizeGak = 0;
		int sizePay = 0;
		String  gakGyom ="";
		List outdata = new ArrayList();
		Map gakseiMap = new HashMap();
		//出力区分により、基本的な情報を取得する。
		//明細表に関しては取得しない。
		List joinStudentInfoList = null;
		List joinStudentInfoListSort = null;	//ソート済リスト
		List gakseiList =new ArrayList();
		
		//対象学生(+納付金割当情報)取得（学籍番号でソート済）
		if(kbn.equals(PAY)){
			joinStudentInfoListSort=getStudentInfo();
		}else {
			joinStudentInfoList=getStudentInfo();
		}
		if (kbn.equals(PAY)) {
			//【納付金指定】--------------------------
			if(joinStudentInfoListSort.size() == 0){
				//対象0件の場合は終了
				return outdata;
			}
		}else{
			//【納付金指定】以外----------------------
			if(joinStudentInfoList.size() == 0){
				//対象0件の場合は終了
				return outdata;
			}
			sizeGak = joinStudentInfoList.size();
		}
		
		
		
		//--------------------------------------------------------------------------------
		//画面.学籍番号指定順にリストの並替えを行う
		//--------------------------------------------------------------------------------
		if (!kbn.equals(PAY)) {
			
			List tmpList = new ArrayList();
			Map sortWork = new HashMap();
			String nextGakusekiCd="";
			
			
			//①並替えの為、一旦リストをHashに格納----------------------------------------------------
			for (int i = 0; i < sizeGak; i++) {
				JoinStudentPayInfoAR joinStudentInfoAR =(JoinStudentPayInfoAR)joinStudentInfoList.get(i);
				String wGakusekiCd= joinStudentInfoAR.getGakusekiCd();
				
				if(i == sizeGak-1){
					//最後の一件の場合はブランク
					nextGakusekiCd = "";
				}else{
					//次のレコードの学籍番号を取得
					JoinStudentPayInfoAR joinStudentInfoARNext =(JoinStudentPayInfoAR)joinStudentInfoList.get(i+1);
					nextGakusekiCd= joinStudentInfoARNext.getGakusekiCd();
				}
				
				if(wGakusekiCd.equals(nextGakusekiCd)){
					//学籍番号が同じならば、ARをワークリストに追加
					tmpList.add(joinStudentInfoAR);
					if(i == sizeGak-1){
						//最後のレコードならばハッシュに追加
						sortWork.put(wGakusekiCd,new ArrayList(tmpList));
						
						tmpList.clear();
					}
				}else{
					
					tmpList.add(joinStudentInfoAR);
					//学籍番号リストを取得
					outGakusekiCdList.add(wGakusekiCd);
					//学籍番号が違えば、ワークリストをハッシュに追加
					sortWork.put(wGakusekiCd,new ArrayList(tmpList));
					tmpList.clear();
					
				}
			}
			
			//②画面.学籍番号指定順にハッシュよりリストを取り出して並び替える---------------------------
			joinStudentInfoListSort = new ArrayList();
			for (int i = 0; i < outGakusekiCdList.size(); i++) {
				//学籍番号取得
				String gakusekiCd = outGakusekiCdList.get(i).toString();
				//ハッシュより学籍番号毎のListを取得
				List addList = (List)sortWork.get(gakusekiCd);
				if(addList==null){
					continue;
				}
				for (int j = 0; j < addList.size(); j++) {
					JoinStudentPayInfoAR joinStudentInfoAR =(JoinStudentPayInfoAR)addList.get(j);
					joinStudentInfoListSort.add(joinStudentInfoAR);
				}
			}
		}
		
		utiwakeList = new ArrayList();
		Map utiwakeMap = new HashMap();
		Map joinPaywBunInfoMap = new HashMap();
		Map wkJoinBunkatsuGakuMap = new HashMap();
		
		/*****************************
		 * 生徒情報要素をループ
		 *****************************/
		sizeGak = joinStudentInfoListSort.size();
		for (int i = 0; i < sizeGak; i++) {
			
			JoinStudentPayInfoAR joinStudentInfoAR =
				(JoinStudentPayInfoAR)joinStudentInfoListSort.get(i);
			
			long kanriNo = joinStudentInfoAR.getKanriNo();
			
			//納付金情報を取得
			Xrm00101L01Bean bean = new Xrm00101L01Bean();
			bean.setNendo(String.valueOf(joinStudentInfoAR.getNendo()));
			bean.setPayCd(joinStudentInfoAR.getPayCd());
			bean.setPatternCd(joinStudentInfoAR.getPatternCd());
			bean.setBunnoKbnCd(String.valueOf(joinStudentInfoAR.getBunnoKbnCd()));
			bean.setBunkatsuNo(String.valueOf(joinStudentInfoAR.getBunkatsuNo()));
			
			
			//納付金配当情報取得
			//条件に合致する納付金配当情報を検索
			GhgPayhDAO ghgPayhDAO = (
					GhgPayhDAO)getDbs().getDao(GhgPayhDAO.class);
			GhgPayhAR ghgPayhAR = ghgPayhDAO.findByPrimaryKey(
					Integer.parseInt(bean.getNendo()),bean.getPayCd(),bean.getPatternCd(),Integer.parseInt(bean.getBunnoKbnCd()));
			
			//発行対象状況区分
			int nendo = 0;
			if (kbn.equals(GAK)) {
				nendo = Integer.parseInt(bean.getNendo());
			} else {
				nendo = gnend;
			}
			String payCd = UtilStr.cnvNull(bean.getPayCd());
			String patternCd = UtilStr.cnvNull(bean.getPatternCd());
			String strBunnoKbnCd =
				UtilStr.cnvNull(bean.getBunnoKbnCd());
			int bunnoKbnCd = 0;
			if (!strBunnoKbnCd.equals("")) {
				bunnoKbnCd = Integer.parseInt(strBunnoKbnCd);
			}
			String strBunkatsuNo =
				UtilStr.cnvNull(bean.getBunkatsuNo());
			int bunkatsuNo = 0;
			if (!strBunkatsuNo.equals("")) {
				bunkatsuNo = Integer.parseInt(strBunkatsuNo);
			}
			
			//DAO生成
			//割当分納情報(結合DAO)
			JoinPaywBunInfoDAO joinPaywBunInfoDAO =
				(JoinPaywBunInfoDAO)getDbs().getDao(JoinPaywBunInfoDAO.class);
			
			JoinBunkatsuGakuDAO joinBunkatsuGakuDAO =
				(JoinBunkatsuGakuDAO) getDbs()
				.getDao(JoinBunkatsuGakuDAO.class);
			
			JoinUtiwakeGakuDAO joinUtiwakeGakuDAO =
				(JoinUtiwakeGakuDAO) getDbs()
				.getDao(JoinUtiwakeGakuDAO.class);
			
			
			StringBuffer utiwakeMapKey = new StringBuffer();
			utiwakeMapKey.append(nendo).append("|");
			utiwakeMapKey.append(payCd).append("|");
			utiwakeMapKey.append(patternCd).append("|");
			utiwakeMapKey.append(bunnoKbnCd);
			
			if(!utiwakeMap.containsKey(utiwakeMapKey.toString())){
				
				List wkList = joinUtiwakeGakuDAO.findExistsPayw(nendo, payCd, patternCd, bunnoKbnCd);
				
				if (wkList != null ){
					
					//内訳金額が0円の内訳を除外したリストを作成
					List joinUtiwakeGakuList = new ArrayList();
					for (int j = 0; j < wkList.size(); j++) {
						JoinUtiwakeGakuAR ar = (JoinUtiwakeGakuAR) wkList.get(j);
						//内訳金額が0円でなければ追加
						if (ar.getPayItemGaku() != 0) joinUtiwakeGakuList.add(ar);
					}
					
					
					
					utiwakeList.addAll(joinUtiwakeGakuList);
				}
				
				//重複した内訳のリストが作成されないようにMAPにリストにAdd済のKey情報をセット
				utiwakeMap.put(utiwakeMapKey.toString(),utiwakeList);
			}
			
			List joinPaywBunInfoList = new ArrayList();
			List wkJoinBunkatsuGakuList = new ArrayList();
			StringBuffer joinPaywBunInfoMapKey = new StringBuffer();
			joinPaywBunInfoMapKey.append(nendo).append("|");
			joinPaywBunInfoMapKey.append(payCd).append("|");
			joinPaywBunInfoMapKey.append(patternCd).append("|");
			joinPaywBunInfoMapKey.append(bunnoKbnCd).append("|");;
			joinPaywBunInfoMapKey.append(bunkatsuNo);
			
			if(!joinPaywBunInfoMap.containsKey(joinPaywBunInfoMapKey.toString())){
				//年度～分割No単位でのListを格納する（管理No単位では必要ないので、Key情報が変更される場合のみ実行
				if (syutkbn.equals(NEW_HAKKO)) {
					//請求書出力対象であり、請求書が出力されていない納付金情報を検索する
					joinPaywBunInfoList = joinPaywBunInfoDAO.findByNendoToBunkatsuNoNonKanriNoNoufuOutNotNouOut(
							nendo,
							payCd,
							patternCd,
							bunnoKbnCd,
							bunkatsuNo);
				}else if(syutkbn.equals(HAKKO)){
					//請求書出力の場合
					//請求書出力対象である納付金情報を検索する
					joinPaywBunInfoList = joinPaywBunInfoDAO.findByNendoToBunkatsuNoNonKanriNoNoufuOut(
							nendo,
							payCd,
							patternCd,
							bunnoKbnCd,
							bunkatsuNo);
				}else  if(syutkbn.equals(RE_HAKKO)){
					//再発行を行う場合、請求書出力が行われている納付金情報のみを取得する
					XrmJoinPaywBunInfoDAO xrmJoinPaywBunInfoDAO =(XrmJoinPaywBunInfoDAO)getDbs().getDao(XrmJoinPaywBunInfoDAO.class);
					joinPaywBunInfoList= xrmJoinPaywBunInfoDAO.findByNendoToBunkatsuSaisyuturyok(
							nendo,
							payCd,
							patternCd,
							bunnoKbnCd,
							bunkatsuNo);
				}
				
				wkJoinBunkatsuGakuList = joinBunkatsuGakuDAO.findByBunkatsuNoNonKanriNo(
						nendo,
						payCd,
						patternCd,
						bunnoKbnCd,
						bunkatsuNo);
				
				
				joinPaywBunInfoMap.put(joinPaywBunInfoMapKey.toString(),joinPaywBunInfoList);
				wkJoinBunkatsuGakuMap.put(joinPaywBunInfoMapKey.toString(),wkJoinBunkatsuGakuList);
			}else{
				//Mapに存在していれば、MapよりListを取得
				joinPaywBunInfoList = (List)joinPaywBunInfoMap.get(joinPaywBunInfoMapKey.toString());
				wkJoinBunkatsuGakuList = (List)wkJoinBunkatsuGakuMap.get(joinPaywBunInfoMapKey.toString());
			}
			
			JoinPaywBunInfoAR joinPaywBunInfoAR = null;
			for (int k = 0; k < joinPaywBunInfoList.size(); k++) {
				JoinPaywBunInfoAR wkJoinPaywBunInfoAR =
					(JoinPaywBunInfoAR) joinPaywBunInfoList.get(k);
				
				if (wkJoinPaywBunInfoAR.getNendo() == nendo
						&& wkJoinPaywBunInfoAR.getKanriNo() == kanriNo
						&& wkJoinPaywBunInfoAR.getPayCd().equals(payCd)
						&& wkJoinPaywBunInfoAR.getPatternCd().equals(patternCd)
						&& wkJoinPaywBunInfoAR.getBunnoKbnCd() == bunnoKbnCd
						&& wkJoinPaywBunInfoAR.getBunkatsuNo() == bunkatsuNo) {
					
					joinPaywBunInfoAR = wkJoinPaywBunInfoAR;
					break;
				}
			}
			
			//割当がない場合は取得しない
			if (joinPaywBunInfoAR == null) {
				continue;
			}
			
			//納付書取扱期間チェック
			//納付書取扱期間をチェックする場合
			String payOutYm = joinPaywBunInfoAR.getPayOutYm();
			if(payOutYm == null){
				//納付書出力不可なら出力しない
				continue;
			}
			
			//分割NO毎の集計金額を取得
			List joinBunkatsuGakuList = new ArrayList();
			for (int k = 0; k < wkJoinBunkatsuGakuList.size(); k++) {
				JoinBunkatsuGakuAR joinBunkatsuGakuAR =
					(JoinBunkatsuGakuAR) wkJoinBunkatsuGakuList.get(k);
				
				if (joinBunkatsuGakuAR.getNendo() == nendo
						&& joinBunkatsuGakuAR.getKanriNo() == kanriNo
						&& joinBunkatsuGakuAR.getPayCd().equals(payCd)
						&& joinBunkatsuGakuAR.getPatternCd().equals(patternCd)
						&& joinBunkatsuGakuAR.getBunnoKbnCd() == bunnoKbnCd
						&& joinBunkatsuGakuAR.getBunkatsuNo() == bunkatsuNo) {
					
					joinBunkatsuGakuList.add(joinBunkatsuGakuAR);
				}
			}
			
			if (joinBunkatsuGakuList.size() == 0) {
				//ありえないが一応チェック
				continue;
			}
			
			JoinBunkatsuGakuAR joinBunkatsuGakuAR =
				(JoinBunkatsuGakuAR)joinBunkatsuGakuList.get(0);
			if (joinBunkatsuGakuAR == null) {
				//ありえないが一応チェック
				continue;
			}
			
			//処理区分を参照
			
			//CSV出力の場合、明細をカウント
			GhgPaywItemDAO ghgPaywItemDAO =
				(GhgPaywItemDAO) getDbs()
				.getDao(GhgPaywItemDAO.class);
			List wkGhgPaywItemList = ghgPaywItemDAO
			.findByPrimaryKey6(nendo,
					kanriNo,
					payCd,
					patternCd,
					bunnoKbnCd,
					bunkatsuNo);
			
			//内訳金額が0円の内訳を除外したリストを作成
			List ghgPaywItemList = new ArrayList();
			if (wkGhgPaywItemList != null) {
				for (int k = 0; k < wkGhgPaywItemList.size(); k++) {
					GhgPaywItemAR ar = (GhgPaywItemAR) wkGhgPaywItemList.get(k);
					//内訳金額が0円でなければ追加
					if (ar.getPayItemGaku() != 0) ghgPaywItemList.add(ar);
				}
			}
			
			outdataCount += ghgPaywItemList.size();
			
			//学生指定の場合、納付金コードから業務コードを取得
			if(kbn.equals(GAK)){
				XrmPayDAO xrmPayDAO = (XrmPayDAO) getDbs().getDao(XrmPayDAO.class);
				XrmPayAR xrmPayAR = xrmPayDAO.findByPrimaryKey(payCd);
				if(xrmPayAR!=null){
					gakGyom = xrmPayAR.getGyomuCd();
				}
			}
			
			//振込依頼人コード
			GhgFurikomiDAO ghgFurikomiDAO =(GhgFurikomiDAO) getDbs().getDao(GhgFurikomiDAO.class);
			String furikomiCd = "";
			GhgFurikomiAR ghgFurikomiAR = ghgFurikomiDAO
			.findByNenKanPayPatBnnoBnkt(nendo,
					kanriNo,
					payCd,
					patternCd,
					bunnoKbnCd,
					bunkatsuNo);
			if (ghgFurikomiAR != null) {
				furikomiCd = ghgFurikomiAR.getFurikomiIraiCd();
			}
			
			//有効期限
			String yukolimit="";
			XrmPaywBunDAO xrmPayhBunDAO = (XrmPaywBunDAO)getDbs().getDao(XrmPaywBunDAO.class);
			XrmPaywBunAR xrmPaywBunAR = xrmPayhBunDAO.findByPrimaryKey(nendo,
					kanriNo,
					payCd,
					patternCd,
					bunnoKbnCd,
					bunkatsuNo);
			//直接期限入力チェック
			if(yukoDate==null){
				if(xrmPaywBunAR!=null){
					yukolimit = UtilDate.editDate(xrmPaywBunAR.getYukouLimit(),1);
				}
			}else{
				//直接期限を入力時
				yukolimit = UtilDate.editDate(yukoDate,1);
			}
			//再発行時有効期限取得
			if(syutkbn.equals(RE_HAKKO)){
				XrmPayeasyRrkAR xrmPayeasyRrkAR = reHakkoDate(nendo,
						kanriNo,
						payCd,
						patternCd,
						bunnoKbnCd,
						bunkatsuNo);
				yukolimit= UtilDate.editDate(xrmPayeasyRrkAR.getYukouLimit(),1);
				
			}
			//納入期限
			String paylim = "";
			paylim=UtilDate.editDate(joinPaywBunInfoAR.getPayLimit(),1);
			
			//入金済額(入金額 - 返金額)
			long nyukinzumiGaku = joinBunkatsuGakuAR.getSumNyukinGaku() - 
			joinBunkatsuGakuAR.getSumHenkinGaku();
			
			//データをセット
			Xrm001OutDataBean dataBean = new Xrm001OutDataBean();                    
			dataBean.setGakuCD(UtilStr.cnvNull(joinStudentInfoAR.getGakusekiCd()));                        //学籍番号
			
			dataBean.setIdoNo(UtilStr.cnvNull(String.valueOf(joinStudentInfoAR.getIdoNo())));                 //異動ＮＯ
			dataBean.setKanriNo(UtilStr.cnvNull(String.valueOf(kanriNo)));                                    //管理番号
			dataBean.setProKbn(UtilStr.cnvNull(joinStudentInfoAR.getProductKbn()));                       //プロダクト区分
			dataBean.setGaksotKbn(UtilStr.cnvNull(joinStudentInfoAR.getGakSotKbn()));                         //学卒区分
			dataBean.setGaknen(UtilStr.cnvNull(String.valueOf(joinStudentInfoAR.getGakunen())));             //学年
			dataBean.setSzkgkCd(UtilStr.cnvNull(joinStudentInfoAR.getSzkGakkaCd()));                       //所属学科組織コード
			dataBean.setNend(UtilStr.cnvNull(String.valueOf(nendo)));                                        //割当年度
			dataBean.setPayCd(UtilStr.cnvNull(payCd));                                                        //納付金コード
			dataBean.setPatternCd(UtilStr.cnvNull(patternCd));                                                //パターンコード
			dataBean.setBunnokbnCd(UtilStr.cnvNull(String.valueOf(bunnoKbnCd)));                              //分納区分コード
			dataBean.setBunkatuCd(UtilStr.cnvNull(String.valueOf(bunkatsuNo)));                              //分割ＮＯ
			dataBean.setSumItemGaku(UtilStr.cnvNull(String.valueOf(joinBunkatsuGakuAR.getSumItemGaku())));    //納付金額
			dataBean.setNyukinGaku(UtilStr.cnvNull(String.valueOf(nyukinzumiGaku)));  //入金済み金額
			dataBean.setMenjoGaku(UtilStr.cnvNull(String.valueOf(joinBunkatsuGakuAR.getSumMenjGaku())));    //免除金額
			dataBean.setNohukinMesyo(UtilStr.cnvNull(ghgPayhAR.getPayName()));                                          //納付金名称
			dataBean.setBunnokbnMeisyo(UtilStr.cnvNull(joinPaywBunInfoAR.getBunnoKbnName()));                   //分納区分名称
			dataBean.setIraininCD(UtilStr.cnvNull(furikomiCd));
			dataBean.setYukoLimit(UtilStr.cnvNull(yukolimit));
			dataBean.setPaylimit(UtilStr.cnvNull(paylim));
			dataBean.setUtiwake(ghgPaywItemList);
			//学生指定時業務コードを取得	
			if(kbn.equals(GAK)){
				dataBean.setGyomCD(gakGyom);
			}
			
			
			String lineNo = "";
			String gakDataKbn = "";
			//所属学科組織並び順の取得
			//プロダクト区分、学卒区分に従い、情報を取得
			if (joinStudentInfoAR.getProductKbn().equals(ProductKbn.KYOTSU.getCode())) {
				/** 共通管轄 */
				if (joinStudentInfoAR.getGakSotKbn().equals(GakusotsuKbn.GAKUSEI.getCode())) {
					CobSgksDAO cobSgksDAO = (CobSgksDAO) getDbs().getDao(CobSgksDAO.class);
					CobSgksAR cobSgksAR = (CobSgksAR) cobSgksDAO.findByPrimaryKey
					(joinStudentInfoAR.getSzkGakkaCd());
					//ソートに使用する為０埋めした並び順No＋所属学科組織コード
					lineNo = UtilStr.cnvData(2, String.valueOf(cobSgksAR.getSgksLineNo()))
					.concat(joinStudentInfoAR.getSzkGakkaCd());
					
					gakDataKbn = ZAIKOSEI;
					
				}	
			}else{
				/** 学費管轄 */
				if (joinStudentInfoAR.getGakSotKbn().equals(GakusotsuKbn.GAKUSEI.getCode())) {
					/** 学費学生 */
					//学費学生コード＋ダミー並び順No＋所属学科組織コード
					lineNo = DUMMY_LINE_NO.concat(joinStudentInfoAR.getSzkGakkaCd());
					
					gakDataKbn = GAKUHIGAK;
				}	
			}
			
			dataBean.setLineNo(lineNo);      //並び順+所属学科組織コード
			dataBean.setGakDataKbn(gakDataKbn); //学生データ区分(ソート用)
			
			//データ追加
			outdata.add(dataBean);
			
			
			joinPaywBunInfoDAO.destroy();
			joinBunkatsuGakuDAO.destroy();
		}
		
		return outdata;
	}
	
	/**
	 * 生徒情報取得 <br>
	 * （選択タブ）指定された条件を元に生徒情報を取得します。 <br>
	 * 
	 * @return studentList    (List)生徒情報リスト
	 * @throws DbException
	 */
	private List getStudentInfo() throws DbException {
		//DAO生成
		CobGakseiDAO cobGakseiDAO = (CobGakseiDAO)getDbs().getDao(CobGakseiDAO.class);
		VCobGakZaiDAO vCobGakZaiDAO =  (VCobGakZaiDAO)getDbs().getDao(VCobGakZaiDAO.class);
		//DAO生成
		//生徒情報一覧・納付金情報付き(結合DAO)
		JoinStudentPayInfoDAO joinStudentInfoDAO 
		= (JoinStudentPayInfoDAO)getDbs().getDao(JoinStudentPayInfoDAO.class);
		List gakList = null;
		List list = null;
		List reGakList = new ArrayList();
		List payList = new ArrayList();
		//セメスタは使用しないので0固定で格納
		Integer sem=Integer.valueOf("0");
		
		if(outPayList != null && outPayList.size() != 0){
			for (int j = 0; j < outPayList.size(); j++) {
				
				Xrm00101L01Bean payBeanWork = (Xrm00101L01Bean)outPayList.get(j);
				GhPayhBean payBean = new GhPayhBean();
				//年度
				if(kbn.equals(GAK)){
					//学生指定タブの場合は配当一覧の割当年度を使用
					payBean.setNendo(String.valueOf(payBeanWork.getNendo()));   
				}else{
					payBean.setNendo(String.valueOf(gnend));   
				}
				
				//納付金コード
				payBean.setPayCd(payBeanWork.getPayCd());
				//パターンコード
				payBean.setPatternCd(payBeanWork.getPatternCd());
				//分納区分コード
				payBean.setBunnoKbnCd(String.valueOf(payBeanWork.getBunnoKbnCd()));
				//分割NO
				payBean.setBunkatsuNo(String.valueOf(payBeanWork.getBunkatsuNo()));
				
				payList.add(payBean);
			}
		}
		String ne =String.valueOf(this.strGakunen);
		
		//学生指定の場合選択項目が存在しないので、学年を0にする
		int gaknen =0;
		
		if (kbn.equals(GAK) ) {
			//（学生指定タブ）からの実行はここでまず在学生のみを取得（出学予定及び出学者は後続処理にて取得）
			//▼共通学生(出学予定者を含めない)
			List coZaiList = joinStudentInfoDAO.findByZaiCoOnlyPaywListBunNo(
					gaknen,
					sem,
					this.szkGakka,
					this.idoSbt,this.idoDateFrom,this.idoDateTo,
					payList,
					gnend,"","0","","0",
					kanriList
			);
			if (coZaiList != null && coZaiList.size() > 0) {
				reGakList.addAll(coZaiList);			
			}
			if(idoSbt == null || idoSbt.equals("")){
				//異動種別が選択されている場合は、学費学生は対象外
				//▼学費学生(出学予定者を含めない)
				List ghZaiList = joinStudentInfoDAO.findByZaiGhSotyoteiOnlyPaywListBunNo(
						gaknen,
						sem,
						this.szkGakka,
						payList,
						gnend,"","0","","0",
						kanriList
				);
				if (ghZaiList != null && ghZaiList.size() > 0) {
					
					reGakList.addAll(ghZaiList);
				}
			}
		}else{
			//（学生指定タブ）以外からの実行はここで在学生及び出学予定者を取得（出学者は対象外）
			//▼共通学生(出学予定者を含める)
			List coZaiList = joinStudentInfoDAO.findByZaiSotyoteiCoOnlyPaywListBunNo(
					Integer.parseInt(strGakunen),
					sem,
					this.szkGakka,
					this.idoSbt,this.idoDateFrom,this.idoDateTo,
					payList,
					gnend,"","0","","0",
					kanriList
			);
			if (coZaiList != null && coZaiList.size() > 0) {
				//就学種別チェック
				if(!kbn.equals(PAY_GAK)){
					if(!syugaksyubetu.equals("")){
						for(int i=0;i<coZaiList.size();i++){
							JoinStudentPayInfoAR	joinStudentPayInfoAR =(JoinStudentPayInfoAR)coZaiList.get(i);
							CobGakseiAR cobGakseiAR = cobGakseiDAO.findByPrimaryKey(joinStudentPayInfoAR.getKanriNo());
							if(syugaksyubetu.equals(cobGakseiAR.getSyugakSbtCd())){
								reGakList.add(joinStudentPayInfoAR);
							}
						}
					}else{
						reGakList.addAll(coZaiList);
					}				
				}else{
					reGakList.addAll(coZaiList);
				}
			}
			if(idoSbt == null || idoSbt.equals("")){
				//異動種別が選択されている場合は、学費学生は対象外
				//▼学費学生(出学予定者を含める)
				List ghZaiList = joinStudentInfoDAO.findByZaiGhSotyoteiOnlyPaywListBunNo(
						Integer.parseInt(strGakunen),
						sem,
						this.szkGakka,
						payList,
						gnend,"","0","","0",
						kanriList
				);
				if (ghZaiList != null && ghZaiList.size() > 0) {
					//就学種別チェック
					if(!syugaksyubetu.equals("")){
						for(int i=0;i<coZaiList.size();i++){
							JoinStudentPayInfoAR joinStudentPayInfoAR =(JoinStudentPayInfoAR)coZaiList.get(i);
							if(syugaksyubetu.equals(joinStudentPayInfoAR.getSyugakSbtCd())){
								reGakList.add(joinStudentPayInfoAR);
							}
						}
					}else{
						reGakList.addAll(coZaiList);
					}				
					
				}
			}
		}
		//*********（学生指定）の場合は【出学者】も取得************************************
		if (kbn.equals(GAK) ) {
			
			//▼共通学生（出学生）
			List coSotList = joinStudentInfoDAO.findBySotCoOnlyPaywListBunNo(
					gaknen,
					sem,
					this.szkGakka,
					this.idoSbt,this.idoDateFrom,this.idoDateTo,
					payList,
					0,0,
					gnend,"","0","","0",
					kanriList
			);
			if (coSotList != null && coSotList.size() > 0) {
				reGakList.addAll(coSotList);
			}
			//▼学費学生（出学生）
			if(idoSbt == null || idoSbt.equals("")){
				List ghSotList = joinStudentInfoDAO.findBySotGhOnlyPaywListBunNo(
						gaknen,
						sem,
						szkGakka,
						payList,
						0,
						gnend,"","0","","0",
						kanriList
				);
				if (ghSotList != null && ghSotList.size() > 0) {
					reGakList.addAll(ghSotList);
				}
			}
		}
		return reGakList;
	}
	/**
	 * このバッチのバッチ番号を取得します。
	 * @see com.jast.gakuen.framework.batch.BatchBase#getBatNo()
	 * @return バッチ番号
	 */
	public int getBatNo() {
		if (BatchConst.BATKBN_C.equals(batKbn)) {
			return 1;
		} else if (BatchConst.BATKBN_P.equals(batKbn)) {
			return 2;
		} else {
			throw new RuntimeException("無効なバッチ区分です。batKbn=" + batKbn);
		}
	}
	
	/**
	 * このバッチクラスが同時実行可能かどうかを返します。
	 * @see com.jast.gakuen.framework.batch.BatchBase#isMultiLoad()
	 * @return trueのとき同一ユーザで同時実行可能、
	 *		  falseのとき同一ユーザで同時実行不可
	 */
	public boolean isMultiLoad() {
		//同時実行不可
		return true;
	}
	
	/**
	 * このバッチインスタンスのバッチ区分を返します。
	 * <pre>
	 * BatchConst.BATKBN_A 一括登録
	 * BatchConst.BATKBN_R 随時登録
	 * BatchConst.BATKBN_P PDF出力
	 * BatchConst.BATKBN_C CSV出力
	 * BatchConst.BATKBN_T TXT出力
	 * </pre>
	 * @see com.jast.gakuen.framework.batch.BatchBase#getBatKbn()
	 * @return バッチ区分
	 */
	public String getBatKbn() {
		if (batKbn == null) {
			batKbn = BatchConst.BATKBN_C;
		}
		return batKbn;
	}
	
	/**
	 * バッチ実行確認有無を返します。
	 * @see com.jast.gakuen.framework.batch.BatchBase#isExecConfirm()
	 * @return カウント処理完了後にバッチ実行有無の確認を行う場合はtrue
	 */
	public boolean isExecConfirm() {
		return execConfirm;
	}
	
	
	/**
	 *
	 * @return 処理結果
	 */
	protected String exec() {
		//戻り値
		String status = null;
		
		try {
			// 停止フラグ確認
			if (isStop()) {
				// ユーザによるバッチ中断 ※ stop()に遷移
				return BatchConst.STAT_IN_STOP;
			}
			
			//ファイル出力
			//PDF出力
			status = pdfOut();	
			
			status = csvOut();
			
			int undocnt = total - normalCount - errerCount;
			//正常、エラー数を画面出力
			updateRegCount(total, normalCount, 0, errerCount,
					undocnt);
			this.updateCount(total);	
			
		} catch (Exception e) {
			UtilLog.error(this.getClass(), e);
			return BatchConst.STAT_END_ERROR;		
		} 
		return status;
	}
	/**
	 * CSV出力します。
	 * @param  kbn		(String)実行画面区分
	 * @return バッチステータス
	 * @exception Exception
	 */
	private String csvOut() throws Exception {
		
		//ファイルパス取得
		String filePath = BatchUtil.getOutputFilePath(CSV_FILEID,
				this.getBatExecNo(),
				BatchConst.FILETYPE_CSV);	
		//名称取得のDAO生成
		GhcPayDAO ghcPayDAO = (GhcPayDAO) getDbs().getDao(GhcPayDAO.class);
		XrmPayDAO xrmPayDAO = (XrmPayDAO) getDbs().getDao(XrmPayDAO.class);
		
		XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO = (XrxMeiKanriKmkDAO) getDbs().getDao(XrxMeiKanriKmkDAO.class);
		
		XrmUtilCsvWriter cw = new XrmUtilCsvWriter();
		
		try {
			cw.open(filePath, charset);
			int cnt = 0;
			//ヘッダーレコード挿入
			String[] csvHeaderLine = new String[5];
			//01:データ区分（1：固定）
			csvHeaderLine[0] = "1";
			//02:ファイル区分（1：固定）
			csvHeaderLine[1] = "1";
			//03:委託者番号（名称管理：M03,02）
			XrxMeiKanriKmkAR xrxMeiKanriKmkAR = xrxMeiKanriKmkDAO.findByPrimaryKey(syunokikanCd, itakkmk);
			csvHeaderLine[2] = xrxMeiKanriKmkAR.getKmkName1();
			//04:予備（空）
			csvHeaderLine[3] = "";
			//05:ダミー
			csvHeaderLine[4] = "";
			cw.outputLine(csvHeaderLine);
			
			for(int i=0;i<outdt.size();i++){
				String[] outDate=(String[]) outdt.get(i);
				String[] csvLine= new String[22];
				String konbiniCd =createConbininetCd(outDate[14],outDate[15]);
				//請求内容取得
				Xrm001OutDataBean xrm001OutDataBean = new Xrm001OutDataBean();
				xrm001OutDataBean = (Xrm001OutDataBean)baseOutdataList.get(i);
				GhcPayAR ghcPayAR = ghcPayDAO.findByPrimaryKey(xrm001OutDataBean.getPayCd());
				XrmPayAR xrmPayAR = xrmPayDAO.findByPrimaryKey(xrm001OutDataBean.getPayCd());
				//01:データ区分（2：固定）
				csvLine[0] = "2";
				//02:処理区分（0：登録、1：変更、2：取消、3：取消解除、4：他手段消込、5：他手段消込取消）
				//（0：固定）
				csvLine[1] = "0";
				//03:顧客番号（振込依頼人コード）
				csvLine[2] = outDate[14];
				//04:確認番号
				csvLine[3] = outDate[15];
				//05:コンビニネットコード
				csvLine[4] = konbiniCd;
				//06:請求金額
				csvLine[5] = outDate[10];
				//07:請求金額元金
				csvLine[6] = outDate[10];
				//08:延滞金額
				csvLine[7] = "";
				//09:消費税額
				csvLine[8] = "";
				//10:請求内容（カナ）
				csvLine[9] = xrmPayAR.getPayNameKana();
				//11:請求内容（漢字）
				csvLine[10] = ghcPayAR.getPayName();
				//12:お客様氏名（カナ）【半角】
				csvLine[11] = XrmUtilConvert.convertToSmall(outDate[9]);
				//13:お客様氏名（漢字）
				csvLine[12] = outDate[4];
				//14:詳細印字内容（カナ）
				csvLine[13] = "";
				//15:詳細印字内容（漢字）
				csvLine[14] = "";
				//16:詳細表示内容表示区分
				csvLine[15] = "";
				//17:詳細表示内容１（カナ）
				csvLine[16] = "";
				//18:詳細表示内容１（漢字）
				csvLine[17] = "";
				//19:詳細表示内容２（カナ）
				csvLine[18] = "";
				//20:詳細表示内容２（漢字）
				csvLine[19] = "";
				//21:請求情報有効期限
				//csvLine[20] = csvYukolimitCreate(xrm001OutDataBean.getYukoLimit());
				//yyyymmddの形式でセットするようにする。
				csvLine[20] = xrm001OutDataBean.getYukoLimit();
				//22:ダミーをセットする
				csvLine[21] = "";
				
				cw.outputLine(csvLine);
				cnt++;
			}
			//エンドレコード挿入
			//01:データ区分（9：固定）
			//02:データレコード件数
			//03:ダミー
			String[] csvEndLine= new String[]{"9",
					Integer.toString(cnt),
					""
			};
			cw.outputLine(csvEndLine);
			
			
			cw.close();
			normalCount =cnt;
		} catch (Exception e) {
			UtilLog.error(this.getClass(), e);
			cw.close();
		}
		
		this.insertBatFile(CSV_FILEID, filePath);
		return BatchConst.STAT_END_SUCCESS;		
	}
	
	/**
	 * PDF出力します。
	 * @return バッチステータス
	 * @exception Exception
	 */
	private String pdfOut() throws Exception {
		
		
		// 中断チェックを行う件数を取得
		int progressCount = UtilCo.getProgressCount(total);
		int cnt = 0;
		UtilPdfWriter pw = new UtilPdfWriter();
		
		String filePath = BatchUtil.getOutputFilePath(PDF_FILEID,
				this.getBatExecNo(),
				BatchConst.FILETYPE_PDF);
		
		pw.open("xrm", PDF_FILEID, filePath, UtilPdfWriter.MODE_REPORT);
		
		// CONBCD金額チェックの基準金額をiniファイルより取得する。
		String strBaseKingakChk =  XrmUtilIni.getProductParameter(XrmIniConst.XRM,
				XrmIniConst.BASE_KINGAK[0],
				XrmIniConst.BASE_KINGAK[1]);
		
		if( strBaseKingakChk != null && !(strBaseKingakChk.equals("")) ){
			intBaseKingakChk = Integer.parseInt(strBaseKingakChk);
		}
		
		for(int i=0;i<baseOutdataList.size();i++){
			
			long seikyu = 0;
			Xrm001OutDataBean xrm001OutDataBean = new Xrm001OutDataBean();
			xrm001OutDataBean = (Xrm001OutDataBean)baseOutdataList.get(i);
			String[] outDate=new String[]{};
			outDate= getDetailOutData(xrm001OutDataBean);
			outdt.add(outDate);
			//納付金の内訳を取得
			Xrm001OutDataBean payBean=null;
			if(!syutkbn.equals(RE_HAKKO)){
				payBean= getUtiwakePay(xrm001OutDataBean);
			}else{
				payBean=payrirekiUti(xrm001OutDataBean);
			}
			List payutiwake = payBean.getUtiwakeGaku();
			List payNamelist =payBean.getUtiwakeName();
			List payutiwakeNo = payBean.getUtiwakeNO();
			
			//PDF書き出し
			pw.output("郵便番号",outDate[0]);
			pw.output("住所1",outDate[1]);
			pw.output("住所2",outDate[2]);
			pw.output("住所3",outDate[3]);
			pw.output("氏名1",outDate[4]);
			pw.output("学籍番号1","("+outDate[5]+")");
			pw.output("担当部署住所",outDate[6]);
			pw.output("担当部署氏名",outDate[7]);
			pw.output("タイトル1",outDate[8]);
			pw.output("氏名カナ",outDate[9]);
			pw.output("氏名2",outDate[4]);
			pw.output("学籍番号2","("+outDate[5]+")");
			
			pw.output("支払和暦年月日",outDate[11]);
			pw.output("収納機関番号",outDate[12]);
			pw.output("委託者番号1",outDate[13]);
			pw.output("振込依頼人コード1",outDate[14]);
			pw.output("確認番号1",outDate[15]);
			
			//通信欄を設定
			if(!tusinKbn){
				tusinnran=getTeikeibun(xrm001OutDataBean);
			}
			for(int f=0;f<tusinnran.length;f++){
				String tus ="通信欄"+(f+1);
				pw.output(tus,tusinnran[f]);
			}
			pw.output("氏名3",outDate[4]);
			
			
			pw.output("委託者番号2",outDate[13]);
			pw.output("振込依頼人コード2",outDate[14]);
			pw.output("確認番号2",outDate[15]);
			pw.output("タイトル2",outDate[8]);
			pw.output("学籍番号3",outDate[5]);
			pw.output("氏名4",outDate[4]);
			
			pw.output("委託者番号3",outDate[13]);
			pw.output("振込依頼人コード3",outDate[14]);
			
			
			pw.output("郵便バーコード",outDate[16]);
			
			//請求金額が30万を超えている場合
			int seikin =0;
			//請求金額チェック
			if(!outDate[10].equals("")){
				seikin = Integer.parseInt(outDate[10]);
			}
			if(seikin>300000){
				pw.output("SVS備考1",SVS1);
				pw.output("SVS備考2",SVS2);
				pw.output("SVS備考3",SVS3);	
				pw.output("CVSDATA1","");
				pw.output("CONBCD","");
			}else{
				pw.output("SVS備考1","");
				pw.output("SVS備考2","");
				pw.output("SVS備考3","");		
				pw.output("CVSDATA1",outDate[17]);
				pw.output("CONBCD",outDate[17]);
			}
			
			//ファイルの存在確認
			if (isExistFile(PAYEASY_IMAGE)) {
				//画像ファイルのパスを登録
				pw.output("ぺイジーマーク",PAYEASY_IMAGE);
			}
			if (isExistFile(SYUNO_IMAGE)) {
				pw.output("収納日付印1",SYUNO_IMAGE);
				pw.output("収納日付印2",SYUNO_IMAGE);
			}
			if (isExistFile(SYUNO_IMAGE_B)) {
				pw.output("収納日付印3",SYUNO_IMAGE_B);
			}
			//内訳金額、名称書き込み
			if(payutiwake!=null){
				for(int f=0;f<payutiwake.size();f++){
					String paygak = (String)payutiwake.get(f);
					String payName =(String)payNamelist.get(f);
					//seikyu =seikyu+Long.parseLong(paygak);
					pw.output("内訳金額", XrmUtilConvert.toNoNullStr(paygak));
					pw.output("内訳名称", XrmUtilConvert.toNoNullStr(payName));
					pw.next();
				}	
			}
			
			//改頁フラグを設定
			pw.output(GhConstant.GH_KAI_PAGE_KEY, i);
			//新規発行の場合
			if(!syutkbn.equals(RE_HAKKO)){
				
				//テーブル編集のための出力したデータを格納
				xrm001OutDataBean.setTitle(outDate[8]);
				xrm001OutDataBean.setWareki(outDate[11]);
				xrm001OutDataBean.setSyunoCD(outDate[12]);
				xrm001OutDataBean.setItakuCD(outDate[13]);
				xrm001OutDataBean.setIraininCD(outDate[14]);
				xrm001OutDataBean.setKakuninCD(outDate[15]);
				//cvsデータをテーブル用に分割
				String[] cvsDate =  separateCvsCd(outDate[17]);
				//300000以上の場合の設定
				if(seikin>300000){
					xrm001OutDataBean.setSVSbiko1(SVS1);
					xrm001OutDataBean.setSVSbiko2(SVS2);
					xrm001OutDataBean.setSVSbiko3(SVS3);
				}else{
					xrm001OutDataBean.setSVSbiko1("");
					xrm001OutDataBean.setSVSbiko2("");
					xrm001OutDataBean.setSVSbiko3("");
				}
				xrm001OutDataBean.setCVSDate1(cvsDate[0]);
				xrm001OutDataBean.setCVSDate2(cvsDate[1]);
				xrm001OutDataBean.setConbcd(outDate[17]);
				
				xrm001OutDataBean.setUtiwakeGaku(payutiwake);
				xrm001OutDataBean.setUtiwakeName(payNamelist);
				xrm001OutDataBean.setUtiwakeNO(payutiwakeNo);
				
				//結合修正箇所
				xrm001OutDataBean.setName(outDate[4]);
				xrm001OutDataBean.setNameKana(outDate[9]);
				xrm001OutDataBean.setTantouAdres(outDate[6]);
				xrm001OutDataBean.setTantouName(outDate[7]);
				xrm001OutDataBean.setSyunoCD(outDate[12]);
				
				//ペイジー履歴作成
				payeasyrirekiInsert(xrm001OutDataBean);
				//ペイジー履歴内訳作成
				payeasyutiwakerirekiInsert(xrm001OutDataBean);
			}
			
			//出力データ更新
			boolean retStore =storeGhgPaywBun(xrm001OutDataBean);
			if (!retStore) {
				return BatchConst.STAT_END_ERROR;
			}
			
			cnt++;
			if (cnt % progressCount == 0) {
				//中断チェック
				if (isStop()) {
					return BatchConst.STAT_IN_STOP;
				}
			}
		}
		//コミットを行う
		getDbs().commit();
		pw.close();
		this.insertBatFile(PDF_FILEID, filePath);
		return BatchConst.STAT_END_SUCCESS;
		
	}
	/**
	 * 納付金割当分納を更新します。
	 * @param nendo 年度
	 * @param kanriNo 管理番号
	 * @param payCd 納付金コード
	 * @param patternCd パターンコード
	 * @param bunnoKbnCd 分納区分コード
	 * @param bunkatsuNo 分割ＮＯ
	 * @return true/false
	 * @throws Exception
	 */
	private boolean storeGhgPaywBun(Xrm001OutDataBean xrm001OutDataBean) throws Exception {
		
		//納付金割当分納
		GhgPaywBunDAO ghgPaywBunDAO =
			(GhgPaywBunDAO) getDbs().getDao(GhgPaywBunDAO.class);
		
		//プライマリーキーでARを読む
		GhgPaywBunAR ghgPaywBunAR = ghgPaywBunDAO
		.findByPrimaryKey(Integer.parseInt(xrm001OutDataBean.getNend()),
				Long.parseLong(xrm001OutDataBean.getKanriNo()),
				xrm001OutDataBean.getPayCd(),
				xrm001OutDataBean.getPatternCd(),
				Integer.parseInt(xrm001OutDataBean.getBunnokbnCd()),
				Integer.parseInt(xrm001OutDataBean.getBunkatuCd()));
		if (ghgPaywBunAR != null) {
			//出力日付
			ghgPaywBunAR.setPayOutputDate(UtilDate.cnvSqlDate(hakkoDate));
			ghgPaywBunAR.store();
		} else {
			//ありえないが一応チェック
			return false;
		}
		return true;
	}
	/**
	 * データの詳細を取得
	 * @param dataBean
	 * @return
	 * @throws Exception
	 */
	private String[] getDetailOutData(Xrm001OutDataBean dataBean) throws Exception {
		int sizeBase = 0;
		int sizePay = 0;
		String nouKeisyoKbnName = null;
		String lblKeisyoKbnName = null;
		
		String[] outPdfdata = new String[]{};		
		//各データリストの初期化
		outdataPdfList = new ArrayList();
		
		//バーコード作成クラス
		UtilBarcode barc = new UtilBarcode();
		
		String[] gakkaName = null;
		//DAO
		GhgPaywBunDAO ghgPaywBunDAO =
			(GhgPaywBunDAO) getDbs().getDao(GhgPaywBunDAO.class);
		GhgPaywItemDAO ghgPaywItemDAO =
			(GhgPaywItemDAO) getDbs().getDao(GhgPaywItemDAO.class);
		GhgFurikomiDAO ghgFurikomiDAO =
			(GhgFurikomiDAO) getDbs().getDao(GhgFurikomiDAO.class);
		CobGaksekiDAO cobGaksekiDAO =
			(CobGaksekiDAO) getDbs().getDao(CobGaksekiDAO.class);
		CobGakseiDAO cobGakseiDAO = 
			(CobGakseiDAO) getDbs().getDao(CobGakseiDAO.class);
		CobSotGakDAO cobSotGakDAO =
			(CobSotGakDAO) getDbs().getDao(CobSotGakDAO.class);
		GhpSotGakDAO ghpSotGakDAO =
			(GhpSotGakDAO) getDbs().getDao(GhpSotGakDAO.class);
		CobSotDAO cobSotDAO =
			(CobSotDAO) getDbs().getDao(CobSotDAO.class);
		GheGakseiDAO gheGakseiDAO =
			(GheGakseiDAO) getDbs().getDao(GheGakseiDAO.class);
		GhpSotDAO ghpSotDAO =			(GhpSotDAO) getDbs().getDao(GhpSotDAO.class);
		CobGakAtskDAO cobGakAtskDAO =
			(CobGakAtskDAO) getDbs().getDao(CobGakAtskDAO.class);
		GheGakAtskDAO gheGakAtskDAO =
			(GheGakAtskDAO) getDbs().getDao(GheGakAtskDAO.class);
		CobGakHsyDAO cobGakHsyDAO =
			(CobGakHsyDAO) getDbs().getDao(CobGakHsyDAO.class);
		GheHsyDAO gheHsyDAO =
			(GheHsyDAO) getDbs().getDao(GheHsyDAO.class);
		CobSotSgksDAO cobSotSgksDAO =
			(CobSotSgksDAO) getDbs().getDao(CobSotSgksDAO.class);
		GheSgksDAO gheSgksDAO =
			(GheSgksDAO) getDbs().getDao(GheSgksDAO.class);
		GhpSotSgksDAO ghpSotSgksDAO =
			(GhpSotSgksDAO) getDbs().getDao(GhpSotSgksDAO.class);
		CozGakoDAO cozGakoDAO =
			(CozGakoDAO) getDbs().getDao(CozGakoDAO.class);
		GhiEnnoDAO ghiEnnoDAO =
			(GhiEnnoDAO) getDbs().getDao(GhiEnnoDAO.class);
		UtilGhStudentName ghUtilStudentName =
			new UtilGhStudentName();
		XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO =
			(XrxMeiKanriKmkDAO) getDbs().getDao(XrxMeiKanriKmkDAO.class);
		XrmPayeasyRrkItemDAO xrmPayeasyRrkItemDAO =
			(XrmPayeasyRrkItemDAO) getDbs().getDao(XrmPayeasyRrkItemDAO.class);
		
		try{
			String yubinCd ="";		//郵便番号
			String adres1 = "";		//住所１
			String adres2 ="";		//住所２
			String adres3 ="";		//住所３
			String name ="";		//氏名
			String gakuNo ="";		//学籍番号
			String tantoAdres ="";	//担当部署住所
			String tantoName ="";	//担当部署名
			String title ="";		//タイトル
			String nameKana ="";	//氏名カナ
			String sekyugaku ="";	//請求金額
			String warekiLimit ="";	//支払和暦年月日
			String syunokikanCd ="";//収納機関番号
			String itaksya ="";		//委託者番号
			String furikomiCd="";//振込依頼人コード
			String kakuninNo ="";	//確認番号
			String CVSDate1 ="";	//CVSデータ1
			String CVSDate2 ="";	//CVSデータ2
			String tusinTxt1 ="";	//通信欄1
			String tusinTxt2 ="";	//通信欄2
			String tusinTxt3 ="";	//通信欄3
			String tusinTxt4 ="";	//通信欄4
			String tusinTxt5 ="";	//通信欄5
			String tusinTxt6 ="";	//通信欄6
			String goke		="";	//合計金額
			String utiwakeName =""; //内訳名称
			String utiwakeGaku =""; //内訳金額
			String szkGakkaCd = ""; //所属学科コード
			String conbcd="";	//conbcd
			//csv
			//顧客番号
			String kokyakNo ="";
			//コンビニネットコード
			String cbnnetCD ="";
			//請求金額
			String seikyuGaku ="";
			//元金
			String gankin="";
			//延滞金額
			String enti ="";
			//消費税
			String tax ="";
			//請求内容カナ
			String seikyuKana ="";
			//請求内容漢字
			String keikyuKanzi ="";
			//請求情報有効期限
			String sekyuLimit ="";
			
			//宛名ラベル敬称を取得
			CozAtnaKsyoDAO cozAtnaKsyoDao =
				(CozAtnaKsyoDAO) getDbs().getDao(CozAtnaKsyoDAO.class);
			CozAtnaKsyoAR cozAtnaKsyoAR = null;
			
			//学生・帰省先
			cozAtnaKsyoAR = cozAtnaKsyoDao
			.findByPrimaryKey(KeishoKbn.GAKUSEI.getCode());
			if (cozAtnaKsyoAR != null) {
				keisyoKbnNameGak = UtilStr.cnvNull(cozAtnaKsyoAR.getKeisyo());
			}
			
			//保証人
			cozAtnaKsyoAR = cozAtnaKsyoDao
			.findByPrimaryKey(KeishoKbn.HOSHONIN.getCode());
			if (cozAtnaKsyoAR != null) {
				keisyoKbnNameHsy = UtilStr.cnvNull(cozAtnaKsyoAR.getKeisyo());
			}
			
			
			//発行日付
			String hakkoday	= UtilDate.editDate(hakkoDate, 2);
			
			//学費年度
			UtilGhGyomuNendo ghUtilGyomuNendo = new UtilGhGyomuNendo();
			int gyomuNendo = ghUtilGyomuNendo.getGyomuNendoInt(getDbs());
			String ghNendo	= String.valueOf(gyomuNendo);
			
			//管理番号
			long kanriNo = Long.parseLong(dataBean.getKanriNo());
			
			//割当年度
			int paywNendo = Integer.parseInt(dataBean.getNend());
			
			//納付金コード
			String payCd = dataBean.getPayCd();
			//パターンコード
			String patternCd = dataBean.getPatternCd();
			//分納区分コード
			int bunnoKbnCd = Integer.parseInt(dataBean.getBunnokbnCd());
			//分割ＮＯ
			int bunkatsuNo = Integer.parseInt(dataBean.getBunkatuCd());
			
			//要徴収金額
			String yotyosyuKingaku =String.valueOf(Long.parseLong(dataBean.getSumItemGaku())-
					Long.parseLong(dataBean.getMenjoGaku()));
			//滞納額
			long tainoItemKin = Long.parseLong(yotyosyuKingaku) -
			Long.parseLong(dataBean.getNyukinGaku());
			if (tainoItemKin < 0) {
				//入金金額の方が多ければ滞納金なし
				tainoItemKin = 0L;
			}
			//納入期限
			GhgPaywBunAR ghgPaywBunAR = ghgPaywBunDAO
			.findByPrimaryKey(paywNendo,
					kanriNo,
					payCd,
					patternCd,
					bunnoKbnCd,
					bunkatsuNo);
			Date payLimit = ghgPaywBunAR.getPayLimit();
			String strPayLimit = "";
			if (payLimit != null) {
				strPayLimit = UtilDate.editDate(payLimit, 2);
			} 
			
			
			
			
			//延納者の場合は延納期限を設定する
			//最新の有効な延納データを取得
			GhiEnnoAR ghiEnnoAR = ghiEnnoDAO.findByActiveEnnoInfo(paywNendo, 
					kanriNo, 
					payCd, 
					patternCd, 
					bunnoKbnCd, 
					bunkatsuNo);
			
			if (ghiEnnoAR != null) {
				//延納受理日
				Date ennouJuriDate = ghiEnnoAR.getEnnouJuriDate();
				//延納期限
				Date ennouLimit = ghiEnnoAR.getEnnouLimit();
				//延納完了日
				Date ennouKanryoDate = ghiEnnoAR.getEnnouKanryoDate();
				
				if (ennouKanryoDate == null
						&& ennouJuriDate != null) {
					//延納状態が｢受理｣の場合
					
					//納入期限に納付金延納.延納期限を設定
					strPayLimit = UtilDate.editDate(ennouLimit, 2);
				}
			}
			
			
			//異動ＮＯ
			int idoNo = Integer.parseInt(dataBean.getIdoNo());
			//プロダクト区分
			String productKbn = dataBean.getProKbn();
			//学卒区分
			String gakSotKbn = dataBean.getGaksotKbn();
			//所属学科組織
			szkGakkaCd = dataBean.getSzkgkCd();
			
			//初期化
			yubinCd ="";		//郵便番号
			adres1 = "";		//住所１
			adres2 ="";		//住所２
			adres3 ="";		//住所３
			name ="";		//氏名
			gakuNo ="";		//学籍番号
			tantoAdres ="";	//担当部署住所
			tantoName ="";	//担当部署名
			title ="";		//タイトル
			nameKana ="";	//氏名カナ
			sekyugaku ="";	//請求金額
			warekiLimit ="";	//支払和暦年月日
			syunokikanCd ="";//収納機関番号
			itaksya ="";		//委託者番号
			conbcd="";			//conbcd
			kakuninNo ="";	//確認番号
			CVSDate1 ="";	//CVSデータ1
			CVSDate2 ="";	//CVSデータ2
			tusinTxt1 ="";	//通信欄1
			tusinTxt2 ="";	//通信欄2
			tusinTxt3 ="";	//通信欄3
			tusinTxt4 ="";	//通信欄4
			tusinTxt5 ="";	//通信欄5
			tusinTxt6 ="";	//通信欄6
			goke		="";	//合計金額
			utiwakeName =""; //内訳名称
			utiwakeGaku =""; //内訳金額
			String nouAddressKbn = "";//送付先住所区分
			String nouHosyoninSbt = "";//保証人種別
			//csv
			//顧客番号
			kokyakNo ="";
			//コンビニネットコード
			cbnnetCD ="";
			//請求金額
			seikyuGaku ="";
			//元金
			gankin="";
			//延滞金額
			enti ="";
			//消費税
			tax ="";
			//請求内容カナ
			seikyuKana ="";
			//請求内容漢字
			keikyuKanzi ="";
			//請求情報有効期限
			sekyuLimit ="";
			
			//学生氏名
			name = ghUtilStudentName.getStudentName(productKbn,
					gakSotKbn,
					kanriNo,
					getDbs());
			//学籍番号
			CobGaksekiAR cobGaksekiAR =	cobGaksekiDAO.findByPrimaryKey(kanriNo, idoNo);
			gakuNo=cobGaksekiAR.getGakusekiCd();
			
			//タイトルを取得し設定する
			GhcPayDAO ghcPayDAO =
				(GhcPayDAO) getDbs().getDao(GhcPayDAO.class);
			GhcPayAR ghcPayAR = ghcPayDAO.findByPrimaryKey(dataBean.getPayCd());
			if(ghcPayAR != null){
				title = ghcPayAR.getPayName();
			}
			
			//プロダクト区分、学卒区分に従い、情報を取得
			if (productKbn.equals(ProductKbn.KYOTSU.getCode())) {
				/** 共通管轄 */
				if(gakSotKbn.equals(GakusotsuKbn.GAKUSEI.getCode())) {
					/** 学生 */
					CobGakseiAR cobGakseiAR =
						cobGakseiDAO.findByPrimaryKey(kanriNo);
					UtilLog.debug(this.getClass(), "ガクセイ：".concat(cobGaksekiAR.getGakusekiCd()));
					//NULLチェック
					if (cobGakseiAR != null) {
						//学生氏名＿カナ
						nameKana = UtilStr.cnvNull(cobGakseiAR.getNameKana());
					}
					
					//宛先（納付書）
					//学籍宛先を検索
					//学生毎の設定
					CobGakAtskAR cobGakAtskAR = cobGakAtskDAO.findByPrimaryKey(kanriNo, ProductKbn.GAKUHI.getCode());
					if (cobGakAtskAR != null) {
						nouAddressKbn =
							String.valueOf(cobGakAtskAR
									.getAtesakiKbn());
						nouHosyoninSbt =
							String.valueOf(cobGakAtskAR
									.getAtesakiHsyNoKm());
					} else {
						// 学生宛先未設定の場合は、デフォルト：学生とする
						nouAddressKbn = AtesakiKbn.GAKUSEI.getCode();
					}
					
					//それぞれのパターンにより、宛先情報を取得
					if (nouAddressKbn
							.equals(AtesakiKbn.GAKUSEI.getCode())) {
						//学生
						if (cobGakseiAR != null) {
							name = UtilStr.cnvNull(cobGakseiAR.getName());
							yubinCd =UtilStr.cnvNull(cobGakseiAR.getAddrCd());
							adres1 = UtilStr.cnvNull(cobGakseiAR.getAddr1());
							adres2 = UtilStr.cnvNull(cobGakseiAR.getAddr2());
							adres3 = UtilStr.cnvNull(cobGakseiAR.getAddr3());
							nouKeisyoKbnName = keisyoKbnNameGak;
						}
					} else if (nouAddressKbn.equals(AtesakiKbn.HOSHONIN.getCode())) {
						//保証人
						CobGakHsyAR cobGakHsyAR =
							cobGakHsyDAO
							.findByPrimaryKey(kanriNo,
									Integer.parseInt(nouHosyoninSbt));
						if (cobGakHsyAR != null) {
							name = UtilStr.cnvNull(cobGakHsyAR.getHsyName());
							yubinCd = UtilStr.cnvNull(cobGakHsyAR.getAddrCd());
							adres1 = UtilStr.cnvNull(cobGakHsyAR.getHsyAddr1());
							adres2 = UtilStr.cnvNull(cobGakHsyAR.getHsyAddr2());
							adres3 = UtilStr.cnvNull(cobGakHsyAR.getHsyAddr3());
							nouKeisyoKbnName = keisyoKbnNameHsy;
						}
					} else if (nouAddressKbn.equals(AtesakiKbn.KISEISAKI.getCode())) {
						//帰省先
						if (cobGakseiAR != null) {
							name = UtilStr.cnvNull(cobGakseiAR.getName());
							yubinCd = UtilStr.cnvNull(cobGakseiAR.getKiseisakiAddrCd());
							adres1 = UtilStr.cnvNull(cobGakseiAR.getKiseisakiAddr1());
							adres2 = UtilStr.cnvNull(cobGakseiAR.getKiseisakiAddr2());
							adres3 = UtilStr.cnvNull(cobGakseiAR.getKiseisakiAddr3());
							nouKeisyoKbnName = keisyoKbnNameGak;
						}
					}					
				}else{
					/** 卒業生 */
					CobSotGakAR cobSotGakAR =cobSotGakDAO.findByPrimaryKey(kanriNo, idoNo);
					CobSotAR cobSotAR = cobSotDAO.findByPrimaryKey(kanriNo);
					
					UtilLog.debug(this.getClass(), "ソツギョウセイ：".concat(cobSotGakAR.getGakusekiCd()));
					
					//NULLチェック
					if (cobSotAR != null) {
						//学生氏名＿カナ
						nameKana = UtilStr.cnvNull(cobSotAR.getNameKana());
					}
					//卒業生学科組織AR
					CobSotSgksAR cobSotSgksAR =
						cobSotSgksDAO
						.findByPrimaryKey(cobSotGakAR
								.getNyugakNendoCur(),
								cobSotGakAR
								.getNyugakGakkiNoCur(),
								szkGakkaCd);
					
					//宛先（納付書）を取得
					//卒業生の場合は卒業生ＴＢＬより取得
					if (cobSotAR != null) {
						name = UtilStr.cnvNull(cobSotAR.getName());
						yubinCd = UtilStr.cnvNull(cobSotAR.getAddrCd());
						adres1 = UtilStr.cnvNull(cobSotAR.getAddr1());
						adres2 = UtilStr.cnvNull(cobSotAR.getAddr2());
						adres3 = UtilStr.cnvNull(cobSotAR.getAddr3());
						nouKeisyoKbnName = keisyoKbnNameGak;
					}
				}
			}else{
				/** 学費管轄 */
				if (gakSotKbn.equals(GakusotsuKbn.GAKUSEI.getCode())) {
					/** 学費学生 */
					GheGakseiAR gheGakseiAR =
						gheGakseiDAO.findByPrimaryKey(kanriNo);
					//NULLチェック
					if (gheGakseiAR != null) {
						//学生氏名＿カナ
						nameKana = gheGakseiAR.getNameKana();
					}
					if (nouAddressKbn.equals(GhConstant.GH_ATSK_GAK_EACH_CODE)) {
						//学生毎の設定
						GheGakAtskAR gheGakAtskAR = gheGakAtskDAO.findByPrimaryKey(kanriNo);
						if (gheGakAtskAR != null) {
							nouAddressKbn =
								String.valueOf(gheGakAtskAR
										.getAtesakiKbn());
							nouHosyoninSbt =
								String.valueOf(gheGakAtskAR
										.getAtesakiHsyNoKm());
						} else {
							// 学費学生宛先未設定の場合は、デフォルト：学生とする
							nouAddressKbn = AtesakiKbn.GAKUSEI.getCode();
						}	
					}
					
					
					//それぞれのパターンにより、宛先情報を取得
					if (nouAddressKbn.equals(AtesakiKbn.GAKUSEI.getCode())) {
						//学生
						if (gheGakseiAR != null) {
							yubinCd = UtilStr.cnvNull(gheGakseiAR.getAddrCd());
							adres1 = UtilStr.cnvNull(gheGakseiAR.getAddr1());
							adres2 = UtilStr.cnvNull(gheGakseiAR.getAddr2());
							adres3 = UtilStr.cnvNull(gheGakseiAR.getAddr3());
							nouKeisyoKbnName = keisyoKbnNameGak;
						}
					} else if (nouAddressKbn.equals(AtesakiKbn.HOSHONIN.getCode())) {
						//保証人
						GheHsyAR gheHsyAR =
							gheHsyDAO
							.findByPrimaryKey(kanriNo,
									Integer.parseInt(nouHosyoninSbt));
						if (gheHsyAR != null) {
							yubinCd = UtilStr.cnvNull(gheHsyAR.getAddrCd());
							adres1 = UtilStr.cnvNull(gheHsyAR.getHsyAddr1());
							adres2 = UtilStr.cnvNull(gheHsyAR.getHsyAddr2());
							adres3 = UtilStr.cnvNull(gheHsyAR.getHsyAddr3());
							nouKeisyoKbnName = keisyoKbnNameGak;
						}
					} else if (nouAddressKbn.equals(AtesakiKbn.KISEISAKI.getCode())) {
						//帰省先
						if (gheGakseiAR != null) {
							yubinCd = UtilStr.cnvNull(gheGakseiAR.getKiseisakiAddrCd());
							adres1 = UtilStr.cnvNull(gheGakseiAR.getKiseisakiAddr1());
							adres2 = UtilStr.cnvNull(gheGakseiAR.getKiseisakiAddr2());
							adres3 = UtilStr.cnvNull(gheGakseiAR.getKiseisakiAddr3());
							nouKeisyoKbnName = keisyoKbnNameGak;
						}
					}
				}else {
					/** 学費卒業生 */
					GhpSotAR ghpSotAR = ghpSotDAO.findByPrimaryKey(kanriNo);
					
					//NULLチェック
					if (ghpSotAR != null) {
						//学生氏名＿カナ
						nameKana = ghpSotAR.getNameKana();
					}
					
					//卒業生学科組織
					GhpSotGakAR ghpSotGakAR =
						ghpSotGakDAO.findByPrimaryKey(kanriNo);
					GhpSotSgksAR ghpSotSgksAR =
						ghpSotSgksDAO
						.findByPrimaryKey(ghpSotGakAR
								.getSyutgakNendo(),
								szkGakkaCd);
					
					//宛先（納付書）を取得
					//学費卒業生の場合は学費卒業生ＴＢＬより取得
					if (ghpSotAR != null) {
						yubinCd = UtilStr.cnvNull(ghpSotAR.getAddrCd());
						adres1 = UtilStr.cnvNull(ghpSotAR.getAddr1());
						adres2 = UtilStr.cnvNull(ghpSotAR.getAddr2());
						adres3 = UtilStr.cnvNull(ghpSotAR.getAddr3());
						nouKeisyoKbnName = keisyoKbnNameGak;
					}
				}
			}
			//バーコード作成
			String barcode =barc.create(getDbs(),yubinCd,adres2,adres3);
			
			
			//出力区分確認
			if(!syutkbn.equals(RE_HAKKO)){
				//請求書新規出力時データ取得
				//担当部署情報取得
				//学生指定時のみbeanから取得
				if(kbn.equals(GAK)){
					gyomcd="";
					gyomcd=dataBean.getGyomCD();
				}
				
				//iniファイルよりスクーリング、教育実習の業務コード取得
				String kaigoini =  XrmUtilIni.getProductParameter(XrmIniConst.XRM,
						XrmIniConst.GYOMU_CD_KYOIKU_GYOMU_CD[0],
						XrmIniConst.GYOMU_CD_KYOIKU_GYOMU_CD[1]);
				String school =  XrmUtilIni.getProductParameter(XrmIniConst.XRM,
						XrmIniConst.GYOMU_CD_SCHOOL_GYOMU_CD[0],
						XrmIniConst.GYOMU_CD_SCHOOL_GYOMU_CD[1]);
				if(kaigoini.indexOf(gyomcd) != -1){
					//教育実習の場合
					XrxMeiKanriKmkAR xrxMeiKanriKmkAR = xrxMeiKanriKmkDAO.findByPrimaryKey(tantosyubet,resercTanto);
					//住所、名称取得
					tantoAdres = xrxMeiKanriKmkAR.getKmkName2();
					tantoName = xrxMeiKanriKmkAR.getKmkName1();
				}else if(gyomcd.equals(school)){
					//スクーリングの場合
					XrxMeiKanriKmkAR xrxMeiKanriKmkAR = xrxMeiKanriKmkDAO.findByPrimaryKey(tantosyubet,schoolTanto);
					//住所、名称取得
					tantoAdres = xrxMeiKanriKmkAR.getKmkName2();
					tantoName = xrxMeiKanriKmkAR.getKmkName1();
				}else{
					//その他の場合
					XrxMeiKanriKmkAR xrxMeiKanriKmkAR = xrxMeiKanriKmkDAO.findByPrimaryKey(tantosyubet,sonotaTanto);
					//住所、名称取得
					tantoAdres = xrxMeiKanriKmkAR.getKmkName2();
					tantoName = xrxMeiKanriKmkAR.getKmkName1();
				}
				
				//収納機関取得
				XrxMeiKanriKmkAR xrxMeiKanriKmkKikanAR = xrxMeiKanriKmkDAO.findByPrimaryKey(this.syunokikanCd,syunokmk);
				if(xrxMeiKanriKmkKikanAR!=null){
					syunokikanCd =UtilStr.cnvNull(xrxMeiKanriKmkKikanAR.getKmkName1());
				}
				
				
				//				//タイトル取得
				//				title=this.title;
				
				//支払期限を和暦で取得
				Date paylim = ghgPaywBunAR.getPayLimit();
				if(nonyuDate==null){
					if(paylim!=null){
						warekiLimit=UtilDate.editDate(paylim, 10);
					}
				}else{
					//直接入力時
					warekiLimit=UtilDate.editDate(nonyuDate, 10);
				}
				//確認番号取得
				/** 2017/06/16 zhao 確認番号最大番号障害対応 Start */
				//kakuninNo = createCheakNo(dataBean.getPayCd());
				kakuninNo = createCheakNo(dataBean);
				/** 2017/06/16 zhao 確認番号最大番号障害対応 End */
				dataBean.setKakuninCD(kakuninNo);
				
				//振込依頼人コード取得	
				furikomiCd =dataBean.getIraininCD();
				
				//内訳金額取得 
				List ghgPaywItemARList =ghgPaywItemDAO.findByPrimaryKey6(ghgPaywBunAR.getNendo(),
						ghgPaywBunAR.getKanriNo(),
						ghgPaywBunAR.getPayCd(),
						ghgPaywBunAR.getPatternCd(),
						ghgPaywBunAR.getBunnoKbnCd(),
						ghgPaywBunAR.getBunkatsuNo()
				);
				int sekyu=0;
				JoinUtiwakeGakuDAO joinUtiwakeGakuDAO =			(JoinUtiwakeGakuDAO) getDbs().getDao(JoinUtiwakeGakuDAO.class);
				for(int n=0;n<ghgPaywItemARList.size();n++){
					GhgPaywItemAR paywItemAR = (GhgPaywItemAR)ghgPaywItemARList.get(n);
					JoinUtiwakeGakuAR joinUtiwakeGakuAR= joinUtiwakeGakuDAO.findByPrimaryKey(paywItemAR.getNendo()
							,paywItemAR.getKanriNo()
							,paywItemAR.getPayCd()
							,paywItemAR.getPatternCd()
							,paywItemAR.getBunnoKbnCd()
							,paywItemAR.getBunkatsuNo()
							,paywItemAR.getPayItemNo());
					//※入金額＝入金済額－返金額をセットする。
					long utigak = joinUtiwakeGakuAR.getSumNyukinGaku() - joinUtiwakeGakuAR.getSumHenkinGaku();
					long nyugak = joinUtiwakeGakuAR.getPayItemGaku() - joinUtiwakeGakuAR.getSumMenjGaku();
					String seikyugak = String.valueOf(nyugak-utigak);
					int sei = Integer.parseInt(seikyugak);
					sekyu =  sekyu+sei;
				}
				sekyugaku=String.valueOf(sekyu);
				dataBean.setSeikyugaku(sekyugaku);
				
				
				//委託者番号取得
				XrxMeiKanriKmkAR xrxMeiKanriKmkItakAR = xrxMeiKanriKmkDAO.findByPrimaryKey(this.syunokikanCd,itakkmk);
				if(xrxMeiKanriKmkItakAR!=null){
					itaksya =UtilStr.cnvNull(xrxMeiKanriKmkItakAR.getKmkName1());
				}
				
				//conbcd番号を取得
				conbcd= createBcd(dataBean);			
			}else{
				//再出力時
				int nend = Integer.parseInt(dataBean.getNend());
				long kanriN = Long.parseLong(dataBean.getKanriNo());
				int bunnokbn = Integer.parseInt(dataBean.getBunnokbnCd());
				int bunkat =Integer.parseInt(dataBean.getBunkatuCd());
				XrmPayeasyRrkAR xrmPayeasyRrkAR = reHakkoDate(nend,
						kanriN,
						dataBean.getPayCd(),
						dataBean.getPatternCd(),
						bunnokbn,
						bunkat);
				if(xrmPayeasyRrkAR!=null){
					
					//結合修正
					//氏名取得
					name = xrmPayeasyRrkAR.getName();
					//氏名カナ取得
					nameKana=xrmPayeasyRrkAR.getNameKana();
					//担当部署住所取得
					tantoAdres=xrmPayeasyRrkAR.getBusyoAdd();
					//担当部署名取得
					tantoName= xrmPayeasyRrkAR.getBusyo();
					//収納機関番号取得
					syunokikanCd=String.valueOf(xrmPayeasyRrkAR.getSyunoNo());
					
					//					//タイトル取得
					//					title=xrmPayeasyRrkAR.getTitle();
					
					//納入期限取得
					//支払期限を和暦で取得
					Date paylim = xrmPayeasyRrkAR.getPayLimit();
					//納入期限を直接入力していない
					if(nonyuDate==null){
						if(paylim!=null){
							warekiLimit=UtilDate.editDate(paylim, 10);
						}
					}else{
						//直接入力時
						warekiLimit=UtilDate.editDate(nonyuDate, 10);
					}
					//委託者番号取得
					itaksya = UtilStr.cnvNull(String.valueOf(xrmPayeasyRrkAR.getItakuNo()));
					
					//振込依頼人コード取得
					furikomiCd = xrmPayeasyRrkAR.getFurikomiIraiCd();
					
					//確認番号取得
					kakuninNo = UtilStr.cnvNull(String.valueOf(xrmPayeasyRrkAR.getCheckNo()));
					
					//conbcd取得
					conbcd = xrmPayeasyRrkAR.getConbCd();
					
					//請求額の取得
					List xrmPayeasyRrkItemARList = xrmPayeasyRrkItemDAO.findByNewItemList(nend,
							kanriN,
							dataBean.getPayCd(),
							dataBean.getPatternCd(),
							bunnokbn,
							bunkat,
							xrmPayeasyRrkAR.getCheckNo());
					int sekyu=0;
					for(int i=0;i<xrmPayeasyRrkItemARList.size();i++){
						XrmPayeasyRrkItemAR xrmPayeasyRrkItemAR = (XrmPayeasyRrkItemAR)xrmPayeasyRrkItemARList.get(i);
						sekyu = sekyu+xrmPayeasyRrkItemAR.getItemGaku();
					}
					sekyugaku=UtilStr.cnvNull(String.valueOf(sekyu));
				}else{
					
				}
				
			}
			if(!yubinCd.equals("")){
				yubinCd=UtilStr.editZipCodeWithHyphen(yubinCd);
			}
			//取得情報を格納
			outPdfdata = new String[]{
					yubinCd,
					adres1,
					adres2,
					adres3,
					name,
					gakuNo,
					tantoAdres,
					tantoName,
					title,
					nameKana,
					sekyugaku,
					warekiLimit,
					syunokikanCd,
					itaksya,
					furikomiCd,
					kakuninNo,
					barcode,
					conbcd
			};
			
		}catch(Exception e){
			UtilLog.error(this.getClass(), e);
			//ロールバック
			getDbs().rollback();
		}
		
		return outPdfdata;
	}
	
	/**
	 * EUCペイジー履歴登録
	 * @param bean
	 * @throws DbException
	 */
	private void payeasyrirekiInsert(Xrm001OutDataBean bean)throws DbException{
		//DAO作成
		XrmPayeasyRrkDAO xrmPayeasyRrkDAO = (XrmPayeasyRrkDAO) getDbs().getDao(XrmPayeasyRrkDAO.class);
		//キーになるデータを取得
		int chkCD =  Integer.parseInt(bean.getKakuninCD());//確認番号
		long kanri = Long.parseLong(bean.getKanriNo());//管理番号
		int bunno =Integer.parseInt(bean.getBunnokbnCd());//分納区分コード
		int bunkatu =Integer.parseInt(bean.getBunkatuCd());//分割番号
		
		//有効期限をjava.sql.date型に変換
		String yuko =bean.getYukoLimit();
		java.util.Date datelim= UtilDate.editDateYYYYMMDD(yuko);
		java.sql.Date yukoLim=UtilDate.cnvSqlDate(datelim);
		//支払期限をjava.sql.date型に変換
		java.util.Date date= UtilDate.editDateYYYYMMDD(bean.getPaylimit());
		java.sql.Date payLim =null;
		if(nonyuDate==null){
			payLim = UtilDate.cnvSqlDate(date);
		}else{
			//直接期限を入力時
			payLim = UtilDate.cnvSqlDate(nonyuDate);
		}
		try{
			//データの存在チェック
			XrmPayeasyRrkAR xrmPayeasyRrkAR =xrmPayeasyRrkDAO.findByPrimaryKey(
					Integer.parseInt(bean.getNend()),
					chkCD,
					kanri,
					bean.getPayCd(),
					bean.getPatternCd(),
					bunno,
					bunkatu);
			if(xrmPayeasyRrkAR==null){
				//データを作成
				xrmPayeasyRrkAR = new XrmPayeasyRrkAR(getDbs(),
						Integer.parseInt(bean.getNend()),
						chkCD,
						kanri,
						bean.getPayCd(),
						bean.getPatternCd(),
						bunno,
						bunkatu);
				//ありえないが有効期限、納付期限が取得できない場合
				if(yukoLim!=null){
					if(!yukoLim.equals("")){
						xrmPayeasyRrkAR.setYukouLimit(yukoLim);
						xrmPayeasyRrkAR.setTitle(UtilStr.cnvNull(bean.getTitle()));
						xrmPayeasyRrkAR.setPayLimit(payLim);
						xrmPayeasyRrkAR.setSyunoNo(Integer.parseInt(bean.getSyunoCD()));
						xrmPayeasyRrkAR.setItakuNo(Integer.parseInt(bean.getItakuCD()));
						xrmPayeasyRrkAR.setFurikomiIraiCd(UtilStr.cnvNull(bean.getIraininCD()));
						xrmPayeasyRrkAR.setSvsRemark1(UtilStr.cnvNull(bean.getSVSbiko1()));
						xrmPayeasyRrkAR.setSvsRemark2(UtilStr.cnvNull(bean.getSVSbiko2()));
						xrmPayeasyRrkAR.setSvsRemark3(UtilStr.cnvNull(bean.getSVSbiko3()));
						xrmPayeasyRrkAR.setCvsData1(UtilStr.cnvNull(bean.getCVSDate1()));
						xrmPayeasyRrkAR.setCvsData2(UtilStr.cnvNull(bean.getCVSDate2()));
						xrmPayeasyRrkAR.setConbCd(UtilStr.cnvNull(bean.getConbcd()));
						//結合修正
						xrmPayeasyRrkAR.setName(UtilStr.cnvNull(bean.getName()));
						xrmPayeasyRrkAR.setNameKana(UtilStr.cnvNull(bean.getNameKana()));
						xrmPayeasyRrkAR.setBusyoAdd(UtilStr.cnvNull(bean.getTantouAdres()));
						xrmPayeasyRrkAR.setBusyo(UtilStr.cnvNull(bean.getTantouName()));
						xrmPayeasyRrkAR.store();
					}
				}
				
			}
		}catch(GakuenException e){
			UtilLog.error(this.getClass(), e);
			//ロールバック
			getDbs().rollback();
			
		}
	}
	
	/**
	 *ペイジー履歴内訳テーブル登録
	 * @param bean
	 * @throws DbException
	 */
	private void payeasyutiwakerirekiInsert(Xrm001OutDataBean bean)throws DbException{
		//ペイジー履歴内訳DAO
		XrmPayeasyRrkItemDAO xrmPayeasyRrkItemDAO = (XrmPayeasyRrkItemDAO) getDbs().getDao(XrmPayeasyRrkItemDAO.class);
		//キーになるデータを取得
		int chkCD =  Integer.parseInt(bean.getKakuninCD());//確認番号
		long kanri = Long.parseLong(bean.getKanriNo());//管理番号
		int bunno =Integer.parseInt(bean.getBunnokbnCd());//分納区分コード
		int bunkatu =Integer.parseInt(bean.getBunkatuCd());//分割番号
		List itemNO =bean.getUtiwakeNO();//内訳NO
		List itemName =bean.getUtiwakeName();//内訳名称
		List itemGaku =bean.getUtiwakeGaku();//内訳金額
		try{
			//内訳数分繰り返し
			for(int i=0;i<itemNO.size();i++){
				//データの存在チェック
				XrmPayeasyRrkItemAR xrmPayeasyRrkItemAR = xrmPayeasyRrkItemDAO.findByPrimaryKey(
						Integer.parseInt(bean.getNend()),
						chkCD,
						kanri,
						bean.getPayCd(),
						bean.getPatternCd(),
						bunno,
						bunkatu,
						Integer.parseInt(String.valueOf(itemNO.get(i)))
				);
				if(xrmPayeasyRrkItemAR==null){
					xrmPayeasyRrkItemAR =new XrmPayeasyRrkItemAR(getDbs(),
							Integer.parseInt(bean.getNend()),
							chkCD,
							kanri,
							bean.getPayCd(),
							bean.getPatternCd(),
							bunno,
							bunkatu,
							Integer.parseInt(String.valueOf(itemNO.get(i)))
					);
					//0円の場合除外
					if(itemGaku.get(i).equals("0")){
						continue;
					}
					int len = String.valueOf(itemName.get(i)).length();
					xrmPayeasyRrkItemAR.setItemName( String.valueOf(itemName.get(i)));
					xrmPayeasyRrkItemAR.setItemGaku(Integer.parseInt(String.valueOf(itemGaku.get(i))));
					//登録
					xrmPayeasyRrkItemAR.store();
				}
			}
		}catch(GakuenException e){
			UtilLog.error(this.getClass(), e);
			//ロールバック
			getDbs().rollback();	
		}
	}
	
	/** 2017/06/16 zhao 確認番号最大番号障害対応 Start */
	/**
	 * 確認番号を作成
	 * @return String 確認番号
	 */
//	private String createCheakNo(String paycd)throws DbException,GakuenException{
//		String chkNo = "";
//		String checkNo ="";
//		String maxNo="0000";
//		//納付金コードで業務コードの番号取得(1は固定)
//		chkNo="1"+ BizXrmComInfo.getFriGyoumuNo(getDbs(),paycd);
//		//ペイジー履歴テーブルから最大値取得
//		XrmPayeasyRrkDAO xrmPayeasyRrkDAO = (XrmPayeasyRrkDAO)getDbs().getDao(XrmPayeasyRrkDAO.class);
//		maxNo = xrmPayeasyRrkDAO.findByCheckCdMAX(chkNo);
//		//データが存在しない場合
//		if(maxNo.equals("")){
//			maxNo ="0000";
//		}
//		//取得最大値に1+
//		int max = Integer.parseInt(maxNo)+1;
//		//業務コードと最大値を合体
//		checkNo = chkNo+XrmUtilConvert.changZeroPad(String.valueOf(max),4);
//		
//		return checkNo;
//	}
	private String createCheakNo(Xrm001OutDataBean dataBean)throws DbException,GakuenException{
		String chkNo = "";
		String checkNo ="";
		String maxNo="0000";
		//納付金コードで業務コードの番号取得(1は固定)
		chkNo="1"+ BizXrmComInfo.getFriGyoumuNo(getDbs(),dataBean.getPayCd());
		int nendo = Integer.parseInt(dataBean.getNend());
		//ペイジー履歴テーブルから当年度最大値取得
		XrmPayeasyRrkDAO xrmPayeasyRrkDAO = (XrmPayeasyRrkDAO)getDbs().getDao(XrmPayeasyRrkDAO.class);
		maxNo = xrmPayeasyRrkDAO.findByNendoCheckCdMAX(nendo, chkNo);
		//データが存在しない場合
		if(maxNo.equals("")){
			maxNo ="0000";
		} else if (maxNo.equals("9999")) {
			maxNo = xrmPayeasyRrkDAO.findByNendoCheckCdMAX2(nendo, chkNo);
			if (maxNo.equals("")) {
				maxNo ="0000";
			}
		}
		//取得最大値に1+
		int max = Integer.parseInt(maxNo)+1;
		//業務コードと最大値を合体
		checkNo = chkNo+XrmUtilConvert.changZeroPad(String.valueOf(max),4);
		
		return checkNo;
	}
	/** 2017/06/16 zhao 確認番号最大番号障害対応 End */
	
	/**
	 * CONBCD作成処理<br>
	 * 30万円を超えている場合はnullで返却<br>
	 * @param bean
	 * @return
	 * @throws DbException
	 * @throws GakuenException
	 */
	private String createBcd(Xrm001OutDataBean bean)throws DbException,GakuenException{
		String bcd = "";
		String MUFkigyo="";
		String itak ="";
		String kokyak ="";
		String limit ="";
		String kingakChk ="";
		String seikyu ="";
		long kingak=0;
		XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO =
			(XrxMeiKanriKmkDAO) getDbs().getDao(XrxMeiKanriKmkDAO.class);
		//企業コード取得
		XrxMeiKanriKmkAR xrxMeiKanriKmkItakAR = xrxMeiKanriKmkDAO.findByPrimaryKey(this.syunokikanCd,kigyoCd);
		if(xrxMeiKanriKmkItakAR!=null){
			MUFkigyo =UtilStr.cnvNull(xrxMeiKanriKmkItakAR.getKmkName1());
		}
		//委託者番号取得
		xrxMeiKanriKmkItakAR = xrxMeiKanriKmkDAO.findByPrimaryKey(this.syunokikanCd,itakkmk);
		if(xrxMeiKanriKmkItakAR!=null){
			itak =UtilStr.cnvNull(xrxMeiKanriKmkItakAR.getKmkName1());
		}
		//顧客番号
		kokyak = bean.getIraininCD()+bean.getKakuninCD();
		//支払期限
		limit = bean.getYukoLimit();
		//請求金額
		seikyu = bean.getSeikyugaku();
		if(seikyu!=null){
			kingak = Long.parseLong(seikyu);
		}else{
			seikyu="";
		}
		//チェックデジット
		String wkB09="0";
		
		if(kingak < intBaseKingakChk){
			kingakChk="0";
		}else{
			kingakChk="1";
		}
		String wkB01	= "91"; //01-02 AI
		String wkB02	= "9" + MUFkigyo; //03-08 9+MUF企業コード
		String wkB03	= itak; //09-13 委託者番号
		String wkB04	= kokyak; //14-29 顧客番号
		String wkB05	= "0"; //30-30 再発行区分 0固定
		String wkB06 ="";
		if(limit.equals("")){
			wkB06	= limit;
		}else{
			wkB06	= limit.substring(2,8); //31-36 支払期限
		}
		String wkB07	= kingakChk; //XRMiniの設定金額未満："0"　XRMiniの設定金額未満以上："1"
		//請求金額が６桁未満の場合、０で６桁にする
		if(seikyu.length()<6){
			int leng = seikyu.length();
			for(int i=leng;leng<6;leng++){
				seikyu = "0"+seikyu;
			}
		}
		String wkB08    = seikyu;//請求金額
		
		//チェックデジットの計算			
		bcd	= wkB01+wkB02+wkB03+wkB04+wkB05+wkB06+wkB07+wkB08;
		//偶数桁 sum(偶数桁)*3
		int wksumbc1=0;
		for(int i=0; i<bcd.length(); i=i+2) {
			wksumbc1 +=Integer.parseInt(bcd.substring(i,i+1));
		}
		wksumbc1 = wksumbc1*3;
		
		//奇数桁 sum(奇数桁)
		int wksumbc2=0;
		for(int i=1; i<bcd.length(); i=i+2) {
			wksumbc2 +=Integer.parseInt(bcd.substring(i,i+1));
		}
		if((wksumbc1+wksumbc2)%10 >0) {
			wkB09=String.valueOf(10-((wksumbc1+wksumbc2)%10));
		}
		bcd	= wkB01+wkB02+wkB03+wkB04+wkB05+wkB06+wkB07+wkB08+wkB09;
		//wkBcData	= ">F"+wkB01+wkB02+wkB03+wkB04+wkB05+wkB06+wkB07+wkB08+wkB09;
		//例91 908167 99001 1234567890123456 0 080930 0 010500 2	
		return bcd;
	}
	
	/**
	 * Date型のオブジェクトをString型に変換します.
	 */
	public String convertDatetoString(java.sql.Date date) {
		return (new SimpleDateFormat(DATE_PATTERN)).format(date);
	}
	
	/**
	 * コンビニネットコード作成
	 * @param irainiCD
	 * @param kakuninCd
	 * @return conbiniCd
	 */
	private String createConbininetCd(String irainiCD,String kakuninCd){
		String conbiniCd ="";
		conbiniCd = irainiCD+kakuninCd+"0";
		return conbiniCd;
	}
	
	/**
	 * ソート済み請求内容の内訳取得
	 * @param bean
	 * @return paylist
	 */
	private Xrm001OutDataBean getUtiwakePay(Xrm001OutDataBean bean)throws DbException{
		Xrm001OutDataBean paybean = new Xrm001OutDataBean();
		GhgPayhItemDAO ghgPayhItemDAO = (GhgPayhItemDAO) getDbs().getDao(GhgPayhItemDAO.class);
		JoinUtiwakeGakuDAO joinUtiwakeGakuDAO =			(JoinUtiwakeGakuDAO) getDbs().getDao(JoinUtiwakeGakuDAO.class);
		List paygak =new ArrayList();
		List payna =new ArrayList();
		List payNo =new ArrayList();
		List pay =bean.getUtiwake();
		
		
		//内訳NOでソートを行う
		GhgPaywItemARComparator so = new GhgPaywItemARComparator();
		so.asc(GhgPaywItemARComparator.PAY_ITEM_NO);
		Collections.sort(pay, so);
		for(int i=0;i<pay.size();i++){
			//内訳リストから金額取得
			GhgPaywItemAR ghgPaywItemAR =(GhgPaywItemAR)pay.get(i);
			JoinUtiwakeGakuAR joinUtiwakeGakuAR= joinUtiwakeGakuDAO.findByPrimaryKey(ghgPaywItemAR.getNendo()
					,Long.parseLong(bean.getKanriNo())
					,ghgPaywItemAR.getPayCd()
					,ghgPaywItemAR.getPatternCd()
					,ghgPaywItemAR.getBunnoKbnCd()
					,ghgPaywItemAR.getBunkatsuNo()
					,ghgPaywItemAR.getPayItemNo());
			//0円の場合除外
			if(ghgPaywItemAR.getPayItemGaku()==0){
				continue;
			}
			//内訳完納済みは除外
			//※入金額＝入金済額－返金額
			long utigak = joinUtiwakeGakuAR.getSumNyukinGaku() - joinUtiwakeGakuAR.getSumHenkinGaku();
			long nyugak = joinUtiwakeGakuAR.getPayItemGaku() - joinUtiwakeGakuAR.getSumMenjGaku();
			if(nyugak<=utigak){
				continue;
			}
			long seikyugak = nyugak-utigak;
			//納付金取得
			paygak.add(String.valueOf(seikyugak));
			//納付金番号取得
			payNo.add(String.valueOf(ghgPaywItemAR.getPayItemNo()));
			
			//内訳から配当内訳を取得
			//配当内訳を取得
			GhgPayhItemAR ghgPayhItemAR = ghgPayhItemDAO.findByPrimaryKey(ghgPaywItemAR.getNendo(),
					ghgPaywItemAR.getPayCd(),
					ghgPaywItemAR.getPatternCd(),
					ghgPaywItemAR.getBunnoKbnCd(),
					ghgPaywItemAR.getBunkatsuNo(),
					ghgPaywItemAR.getPayItemNo()
			);
			payna.add(ghgPayhItemAR.getItemName());
		}
		paybean.setUtiwakeGaku(paygak);
		paybean.setUtiwakeName(payna);
		paybean.setUtiwakeNO(payNo);
		return paybean;
	}
	/**
	 * ペイジー履歴内訳取得
	 * @param bean
	 * @return
	 * @throws DbException
	 */
	private Xrm001OutDataBean payrirekiUti(Xrm001OutDataBean bean)throws DbException{
		Xrm001OutDataBean paybean = new Xrm001OutDataBean();
		List paygak =new ArrayList();
		List payna =new ArrayList();
		List payNo =new ArrayList();
		List pay =bean.getUtiwake();
		
		XrmPayeasyRrkItemDAO xrmPayeasyRrkItemDAO =
			(XrmPayeasyRrkItemDAO) getDbs().getDao(XrmPayeasyRrkItemDAO.class);
		GhgPaywItemAR ghgPaywItemAR =(GhgPaywItemAR)pay.get(0);
		//ペイジー履歴内訳DAO
		XrmPayeasyRrkDAO xrmPayeasyRrkDAO = (XrmPayeasyRrkDAO) getDbs().getDao(XrmPayeasyRrkDAO.class);
		XrmPayeasyRrkAR xrmPayeasyRrkAR = null;
		XrmPayeasyRrkAR xrmPayeasyRrkAR2 = null; 
		int max=0;
		List  xrmPayeasyRrkARList = xrmPayeasyRrkDAO.findByNotCheckNo(ghgPaywItemAR.getNendo(),
				ghgPaywItemAR.getKanriNo(),
				ghgPaywItemAR.getPayCd(),
				ghgPaywItemAR.getPatternCd(),
				ghgPaywItemAR.getBunnoKbnCd(),
				ghgPaywItemAR.getBunkatsuNo()
		);
		for(int i =0 ; i<xrmPayeasyRrkARList.size();i++){
			xrmPayeasyRrkAR= (XrmPayeasyRrkAR)xrmPayeasyRrkARList.get(i);
			if(max==0){
				max=xrmPayeasyRrkAR.getCheckNo();
			}else{
				int ckMax=xrmPayeasyRrkAR.getCheckNo();
				max = Math.max(max,ckMax);
			}
		}
		
		if(pay.size()!=0){
			for(int i =0;i<pay.size();i++){
				GhgPaywItemAR ghgPaywItemAR2 =(GhgPaywItemAR)pay.get(i);
				//請求額の取得
				XrmPayeasyRrkItemAR xrmPayeasyRrkItemAR = xrmPayeasyRrkItemDAO.findByPrimaryKey(ghgPaywItemAR2.getNendo(),
						max,
						ghgPaywItemAR2.getKanriNo(),
						ghgPaywItemAR2.getPayCd(),
						ghgPaywItemAR2.getPatternCd(),
						ghgPaywItemAR2.getBunnoKbnCd(),
						ghgPaywItemAR2.getBunkatsuNo(),
						ghgPaywItemAR2.getPayItemNo()
				);
				//取得できない場合には、処理を行わない。
				if(xrmPayeasyRrkItemAR != null){
					paygak.add(String.valueOf(xrmPayeasyRrkItemAR.getItemGaku()));
					payna.add(xrmPayeasyRrkItemAR.getItemName());
					payNo.add(String.valueOf(xrmPayeasyRrkItemAR.getPayItemNo()));
				}
			}
			
		}
		paybean.setUtiwakeGaku(paygak);
		paybean.setUtiwakeName(payna);
		paybean.setUtiwakeNO(payNo);
		return paybean;
	}
	/**
	 * cvsDateを分割する
	 * @param cvsCd
	 * @return
	 */
	private String[] separateCvsCd(String cvsCd){
		String[] cvsList=null;
		if(cvsCd!=null){
			if(cvsCd.length()>20){
				String cvs1 = cvsCd.substring(0,20);
				String cvs2 = cvsCd.substring(20);
				cvsList = new String[]{
						cvs1,
						cvs2
				};
			}
		}else{
			cvsList =new String[]{cvsCd," "	};
		}
		return cvsList;
	}
	/**
	 * ファイルの存在確認 <BR>
	 *
	 * @param fileName
	 * @return boolean
	 */
	public static boolean isExistFile(String filePath) {
		
		File file = new File(filePath);
		if (file.exists()) {
			return true;
		}
		
		return false;
	}
	/**
	 * 再出力時のペイジー出力データ取得
	 * @return
	 */
	private XrmPayeasyRrkAR reHakkoDate(int nendo,
			long kanriNo,
			String payCd,
			String patternCd,
			int bunnoKbnCd,
			int bunkatsuNo
	)throws DbException{
		//ペイジー履歴内訳DAO
		XrmPayeasyRrkDAO xrmPayeasyRrkDAO = (XrmPayeasyRrkDAO) getDbs().getDao(XrmPayeasyRrkDAO.class);
		XrmPayeasyRrkAR xrmPayeasyRrkAR = null;
		XrmPayeasyRrkAR xrmPayeasyRrkAR2 = null; 
		int max=0;
		List  xrmPayeasyRrkARList = xrmPayeasyRrkDAO.findByNotCheckNo(nendo,
				kanriNo,
				payCd,
				patternCd,
				bunnoKbnCd,
				bunkatsuNo
		);
		for(int i =0 ; i<xrmPayeasyRrkARList.size();i++){
			xrmPayeasyRrkAR= (XrmPayeasyRrkAR)xrmPayeasyRrkARList.get(i);
			if(max==0){
				max=xrmPayeasyRrkAR.getCheckNo();
			}else{
				int ckMax=xrmPayeasyRrkAR.getCheckNo();
				max = Math.max(max,ckMax);
			}
		}
		xrmPayeasyRrkAR2=xrmPayeasyRrkDAO.findByPrimaryKey(nendo,
				max,
				kanriNo,
				payCd,
				patternCd,
				bunnoKbnCd,
				bunkatsuNo
		);
		return xrmPayeasyRrkAR2;
	}
	/**
	 * CSV出力用の有効期限の作成
	 * @param yukolimit
	 * @return
	 */
	private String csvYukolimitCreate(String yukolimit){
		String csvYukolim = "";
		//3桁以下ならば空で返却
		if(yukolimit.length()>2){
			//2～8桁
			csvYukolim = yukolimit.substring(2,8);
		}
		return csvYukolim;
	}
	/**
	 * 配当から通信欄の定型文取得
	 * @param bean
	 * @return
	 * @throws DbException
	 */
	private String[] getTeikeibun(Xrm001OutDataBean bean)throws DbException{
		String[] teikeibun = null;
		//OS依存の改行コードを取得
		String crlf = System.getProperty("line.separator");
		XrmPayhDAO xrmPayhDAO = (XrmPayhDAO) getDbs().getDao(XrmPayhDAO.class);
		XrmPayhAR xrmPayhAR = xrmPayhDAO.findByPrimaryKey(Integer.parseInt(bean.getNend()),
				bean.getPayCd(),
				bean.getPatternCd(),
				Integer.parseInt(bean.getBunnokbnCd()));
		String strTsushinText =xrmPayhAR.getTsushinText();
		// INIファイルの改行コード(\n)をOS依存の改行コードにて置換
		strTsushinText = strTsushinText.replaceAll("\\\\n", crlf);
		
		teikeibun = strTsushinText.split(crlf);
		
		return teikeibun;
	}
}
